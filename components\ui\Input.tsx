import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from '@/hooks/useColorScheme';

interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  required?: boolean;
  disabled?: boolean;
}

export function Input({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  labelStyle,
  required = false,
  disabled = false,
  ...textInputProps
}: InputProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isFocused, setIsFocused] = useState(false);

  const getInputContainerStyle = (): ViewStyle => ({
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: error 
      ? '#F44336' 
      : isFocused 
        ? '#6200EE' 
        : isDark 
          ? '#333333' 
          : '#E0E0E0',
    paddingHorizontal: 16,
    paddingVertical: 12,
    opacity: disabled ? 0.6 : 1,
  });

  const getInputStyle = (): TextStyle => ({
    flex: 1,
    fontSize: 16,
    color: isDark ? '#FFFFFF' : '#000000',
    paddingLeft: leftIcon ? 8 : 0,
    paddingRight: rightIcon ? 8 : 0,
  });

  const getLabelStyle = (): TextStyle => ({
    fontSize: 16,
    fontWeight: '600',
    color: isDark ? '#FFFFFF' : '#000000',
    marginBottom: 8,
  });

  const getIconColor = (): string => {
    if (error) return '#F44336';
    if (isFocused) return '#6200EE';
    return isDark ? '#CCCCCC' : '#666666';
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[getLabelStyle(), labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={getInputContainerStyle()}>
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            color={getIconColor()}
          />
        )}
        
        <TextInput
          {...textInputProps}
          style={[getInputStyle(), inputStyle]}
          onFocus={(e) => {
            setIsFocused(true);
            textInputProps.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            textInputProps.onBlur?.(e);
          }}
          placeholderTextColor={isDark ? '#666666' : '#999999'}
          editable={!disabled}
        />
        
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            <Ionicons
              name={rightIcon}
              size={20}
              color={getIconColor()}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
      
      {hint && !error && (
        <Text style={[styles.hintText, { color: isDark ? '#CCCCCC' : '#666666' }]}>
          {hint}
        </Text>
      )}
    </View>
  );
}

// Specialized input components
interface SearchInputProps extends Omit<InputProps, 'leftIcon'> {
  onClear?: () => void;
  showClearButton?: boolean;
}

export function SearchInput({
  onClear,
  showClearButton = true,
  value,
  ...props
}: SearchInputProps) {
  return (
    <Input
      {...props}
      value={value}
      leftIcon="search"
      rightIcon={showClearButton && value ? "close-circle" : undefined}
      onRightIconPress={onClear}
      placeholder="Search..."
    />
  );
}

interface PasswordInputProps extends Omit<InputProps, 'rightIcon' | 'secureTextEntry'> {
  showPassword?: boolean;
  onTogglePassword?: () => void;
}

export function PasswordInput({
  showPassword = false,
  onTogglePassword,
  ...props
}: PasswordInputProps) {
  const [isPasswordVisible, setIsPasswordVisible] = useState(showPassword);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
    onTogglePassword?.();
  };

  return (
    <Input
      {...props}
      secureTextEntry={!isPasswordVisible}
      rightIcon={isPasswordVisible ? "eye-off" : "eye"}
      onRightIconPress={togglePasswordVisibility}
    />
  );
}

interface TextAreaProps extends Omit<InputProps, 'multiline' | 'numberOfLines'> {
  rows?: number;
  maxLength?: number;
  showCharacterCount?: boolean;
}

export function TextArea({
  rows = 4,
  maxLength,
  showCharacterCount = false,
  value,
  containerStyle,
  ...props
}: TextAreaProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View style={containerStyle}>
      <Input
        {...props}
        value={value}
        multiline
        numberOfLines={rows}
        maxLength={maxLength}
        inputStyle={{
          height: rows * 20 + 24, // Approximate height based on rows
          textAlignVertical: 'top',
        }}
      />
      
      {showCharacterCount && maxLength && (
        <Text style={[
          styles.characterCount,
          { color: isDark ? '#CCCCCC' : '#666666' }
        ]}>
          {(value?.length || 0)}/{maxLength}
        </Text>
      )}
    </View>
  );
}

// Numeric input component
interface NumericInputProps extends Omit<InputProps, 'keyboardType'> {
  min?: number;
  max?: number;
  step?: number;
  showSteppers?: boolean;
  onIncrement?: () => void;
  onDecrement?: () => void;
}

export function NumericInput({
  min,
  max,
  step = 1,
  showSteppers = false,
  onIncrement,
  onDecrement,
  value,
  onChangeText,
  ...props
}: NumericInputProps) {
  const handleIncrement = () => {
    const currentValue = parseFloat(value as string) || 0;
    const newValue = Math.min(currentValue + step, max || Infinity);
    onChangeText?.(newValue.toString());
    onIncrement?.();
  };

  const handleDecrement = () => {
    const currentValue = parseFloat(value as string) || 0;
    const newValue = Math.max(currentValue - step, min || -Infinity);
    onChangeText?.(newValue.toString());
    onDecrement?.();
  };

  if (showSteppers) {
    return (
      <View style={styles.numericContainer}>
        <TouchableOpacity
          style={styles.stepperButton}
          onPress={handleDecrement}
        >
          <Ionicons name="remove" size={20} color="#6200EE" />
        </TouchableOpacity>
        
        <Input
          {...props}
          value={value}
          onChangeText={onChangeText}
          keyboardType="numeric"
          containerStyle={styles.numericInput}
        />
        
        <TouchableOpacity
          style={styles.stepperButton}
          onPress={handleIncrement}
        >
          <Ionicons name="add" size={20} color="#6200EE" />
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <Input
      {...props}
      value={value}
      onChangeText={onChangeText}
      keyboardType="numeric"
    />
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  required: {
    color: '#F44336',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
    marginTop: 4,
  },
  hintText: {
    fontSize: 12,
    marginTop: 4,
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  numericContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  numericInput: {
    flex: 1,
    marginBottom: 0,
  },
  stepperButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#6200EE20',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
