import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { TodoItem, Reminder, CalendarEvent } from '@/types';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export class NotificationService {
  private static isInitialized = false;

  // Initialize notification service
  static async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) return true;

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Notification permissions not granted');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#6200EE',
        });

        await Notifications.setNotificationChannelAsync('reminders', {
          name: 'Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF9800',
        });

        await Notifications.setNotificationChannelAsync('todos', {
          name: 'To-dos',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250],
          lightColor: '#4CAF50',
        });

        await Notifications.setNotificationChannelAsync('calendar', {
          name: 'Calendar Events',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#2196F3',
        });
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
      return false;
    }
  }

  // Schedule notification for a todo item
  static async scheduleTodoNotification(todo: TodoItem): Promise<string | null> {
    try {
      if (!todo.dueDate) return null;

      const trigger = new Date(todo.dueDate);
      const now = new Date();

      // Don't schedule notifications for past dates
      if (trigger <= now) return null;

      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Todo Due',
          body: `"${todo.title}" is due now`,
          data: {
            type: 'todo',
            todoId: todo.id,
            priority: todo.priority,
          },
          categoryIdentifier: 'todo',
          sound: true,
        },
        trigger,
      });

      return identifier;
    } catch (error) {
      console.error('Failed to schedule todo notification:', error);
      return null;
    }
  }

  // Schedule notification for a reminder
  static async scheduleReminderNotification(reminder: Reminder): Promise<string | null> {
    try {
      if (reminder.type !== 'time' || !reminder.triggerTime) return null;

      const trigger = new Date(reminder.triggerTime);
      const now = new Date();

      // Don't schedule notifications for past dates
      if (trigger <= now) return null;

      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Reminder',
          body: reminder.description || reminder.title,
          data: {
            type: 'reminder',
            reminderId: reminder.id,
          },
          categoryIdentifier: 'reminder',
          sound: true,
        },
        trigger,
      });

      return identifier;
    } catch (error) {
      console.error('Failed to schedule reminder notification:', error);
      return null;
    }
  }

  // Schedule notification for a calendar event
  static async scheduleEventNotification(
    event: CalendarEvent,
    minutesBefore: number = 15
  ): Promise<string | null> {
    try {
      const eventStart = new Date(event.startDate);
      const trigger = new Date(eventStart.getTime() - minutesBefore * 60 * 1000);
      const now = new Date();

      // Don't schedule notifications for past dates
      if (trigger <= now) return null;

      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: `Event in ${minutesBefore} minutes`,
          body: `"${event.title}" starts at ${this.formatTime(eventStart)}`,
          data: {
            type: 'event',
            eventId: event.id,
            minutesBefore,
          },
          categoryIdentifier: 'event',
          sound: true,
        },
        trigger,
      });

      return identifier;
    } catch (error) {
      console.error('Failed to schedule event notification:', error);
      return null;
    }
  }

  // Schedule recurring reminder notifications
  static async scheduleRecurringReminder(reminder: Reminder): Promise<string[]> {
    try {
      if (!reminder.isRecurring || !reminder.recurrence || !reminder.triggerTime) {
        return [];
      }

      const identifiers: string[] = [];
      const baseDate = new Date(reminder.triggerTime);
      const now = new Date();
      const endDate = reminder.recurrence.endDate || new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 1 year from now

      let currentDate = new Date(baseDate);
      let count = 0;
      const maxOccurrences = 50; // Limit to prevent too many notifications

      while (currentDate <= endDate && count < maxOccurrences) {
        if (currentDate > now) {
          const identifier = await Notifications.scheduleNotificationAsync({
            content: {
              title: 'Recurring Reminder',
              body: reminder.description || reminder.title,
              data: {
                type: 'reminder',
                reminderId: reminder.id,
                isRecurring: true,
              },
              categoryIdentifier: 'reminder',
              sound: true,
            },
            trigger: currentDate,
          });

          identifiers.push(identifier);
        }

        // Calculate next occurrence
        switch (reminder.recurrence.type) {
          case 'daily':
            currentDate.setDate(currentDate.getDate() + (reminder.recurrence.interval || 1));
            break;
          case 'weekly':
            currentDate.setDate(currentDate.getDate() + 7 * (reminder.recurrence.interval || 1));
            break;
          case 'monthly':
            currentDate.setMonth(currentDate.getMonth() + (reminder.recurrence.interval || 1));
            break;
          case 'yearly':
            currentDate.setFullYear(currentDate.getFullYear() + (reminder.recurrence.interval || 1));
            break;
          default:
            break;
        }

        count++;
      }

      return identifiers;
    } catch (error) {
      console.error('Failed to schedule recurring reminder:', error);
      return [];
    }
  }

  // Cancel notification
  static async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
    } catch (error) {
      console.error('Failed to cancel notification:', error);
    }
  }

  // Cancel all notifications for a specific item
  static async cancelNotificationsForItem(itemId: string, type: 'todo' | 'reminder' | 'event'): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      
      const notificationsToCancel = scheduledNotifications.filter(notification => {
        const data = notification.content.data;
        return (
          (type === 'todo' && data?.todoId === itemId) ||
          (type === 'reminder' && data?.reminderId === itemId) ||
          (type === 'event' && data?.eventId === itemId)
        );
      });

      for (const notification of notificationsToCancel) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }
    } catch (error) {
      console.error('Failed to cancel notifications for item:', error);
    }
  }

  // Cancel all notifications
  static async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to cancel all notifications:', error);
    }
  }

  // Get all scheduled notifications
  static async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error);
      return [];
    }
  }

  // Send immediate notification
  static async sendImmediateNotification(
    title: string,
    body: string,
    data?: any
  ): Promise<string | null> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: true,
        },
        trigger: null, // Send immediately
      });

      return identifier;
    } catch (error) {
      console.error('Failed to send immediate notification:', error);
      return null;
    }
  }

  // Handle notification response (when user taps notification)
  static addNotificationResponseListener(
    listener: (response: Notifications.NotificationResponse) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  // Handle notification received while app is in foreground
  static addNotificationReceivedListener(
    listener: (notification: Notifications.Notification) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationReceivedListener(listener);
  }

  // Get notification settings
  static async getNotificationSettings(): Promise<Notifications.NotificationPermissionsStatus> {
    try {
      return await Notifications.getPermissionsAsync();
    } catch (error) {
      console.error('Failed to get notification settings:', error);
      return {
        status: 'undetermined',
        canAskAgain: true,
        granted: false,
      };
    }
  }

  // Request notification permissions
  static async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Failed to request notification permissions:', error);
      return false;
    }
  }

  // Get push notification token (for remote notifications)
  static async getPushToken(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications only work on physical devices');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync();
      return token.data;
    } catch (error) {
      console.error('Failed to get push token:', error);
      return null;
    }
  }

  // Utility functions
  private static formatTime(date: Date): string {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  private static formatDate(date: Date): string {
    return date.toLocaleDateString();
  }

  // Badge management
  static async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Failed to set badge count:', error);
    }
  }

  static async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('Failed to get badge count:', error);
      return 0;
    }
  }

  static async clearBadge(): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('Failed to clear badge:', error);
    }
  }

  // Notification categories (for iOS action buttons)
  static async setupNotificationCategories(): Promise<void> {
    try {
      await Notifications.setNotificationCategoryAsync('todo', [
        {
          identifier: 'complete',
          buttonTitle: 'Mark Complete',
          options: {
            opensAppToForeground: false,
          },
        },
        {
          identifier: 'snooze',
          buttonTitle: 'Snooze',
          options: {
            opensAppToForeground: false,
          },
        },
      ]);

      await Notifications.setNotificationCategoryAsync('reminder', [
        {
          identifier: 'dismiss',
          buttonTitle: 'Dismiss',
          options: {
            opensAppToForeground: false,
          },
        },
        {
          identifier: 'snooze',
          buttonTitle: 'Snooze',
          options: {
            opensAppToForeground: false,
          },
        },
      ]);

      await Notifications.setNotificationCategoryAsync('event', [
        {
          identifier: 'view',
          buttonTitle: 'View Event',
          options: {
            opensAppToForeground: true,
          },
        },
      ]);
    } catch (error) {
      console.error('Failed to setup notification categories:', error);
    }
  }
}
