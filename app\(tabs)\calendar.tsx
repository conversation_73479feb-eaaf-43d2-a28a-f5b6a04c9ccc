import { useColorScheme } from '@/hooks/useColorScheme';
import { useCalendarStore } from '@/store';
import { CalendarEvent } from '@/types';
import { formatDate, formatDateTime, formatTime } from '@/utils';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import React, { useMemo, useState } from 'react';
import {
    Alert,
    FlatList,
    Modal,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { Calendar } from 'react-native-calendars';
import { SafeAreaView } from 'react-native-safe-area-context';

type ViewMode = 'month' | 'week' | 'day' | 'agenda';

// Helper function for platform-specific shadows
const getShadowStyle = (color = '#000', opacity = 0.1, radius = 4, elevation = 2) => ({
  ...Platform.select({
    ios: {
      shadowColor: color,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: opacity,
      shadowRadius: radius,
    },
    android: {
      elevation: elevation,
    },
    web: {
      boxShadow: `0px 2px ${radius}px rgba(0, 0, 0, ${opacity})`,
    },
  }),
});

export default function CalendarScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const {
    events,
    addEvent,
    updateEvent,
    deleteEvent,
    getEventsByDate,
    getEventsByDateRange,
    getUpcomingEvents,
  } = useCalendarStore();

  const [viewMode, setViewMode] = useState<ViewMode>('month');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    startDate: new Date(),
    endDate: new Date(Date.now() + 60 * 60 * 1000), // 1 hour later
    isAllDay: false,
    location: '',
    color: '#6200EE',
  });

  // Get events for selected date
  const selectedDateEvents = useMemo(() => {
    return getEventsByDate(new Date(selectedDate));
  }, [events, selectedDate, getEventsByDate]);

  // Get upcoming events for agenda view
  const upcomingEvents = useMemo(() => {
    return getUpcomingEvents(30); // Next 30 days
  }, [events, getUpcomingEvents]);

  // Create marked dates for calendar
  const markedDates = useMemo(() => {
    const marked: any = {};
    
    events.forEach(event => {
      const dateKey = new Date(event.startDate).toISOString().split('T')[0];
      if (!marked[dateKey]) {
        marked[dateKey] = { dots: [] };
      }
      marked[dateKey].dots.push({
        color: event.color || '#6200EE',
      });
    });

    // Mark selected date
    if (marked[selectedDate]) {
      marked[selectedDate].selected = true;
      marked[selectedDate].selectedColor = '#6200EE';
    } else {
      marked[selectedDate] = {
        selected: true,
        selectedColor: '#6200EE',
      };
    }

    return marked;
  }, [events, selectedDate]);

  const handleAddEvent = () => {
    if (!newEvent.title.trim()) {
      Alert.alert('Error', 'Please enter a title for the event');
      return;
    }

    if (newEvent.endDate <= newEvent.startDate) {
      Alert.alert('Error', 'End time must be after start time');
      return;
    }

    addEvent({
      title: newEvent.title.trim(),
      description: newEvent.description.trim() || undefined,
      startDate: newEvent.startDate,
      endDate: newEvent.endDate,
      isAllDay: newEvent.isAllDay,
      location: newEvent.location.trim() || undefined,
      color: newEvent.color,
      attendees: [],
      reminders: [],
    });

    // Reset form
    setNewEvent({
      title: '',
      description: '',
      startDate: new Date(),
      endDate: new Date(Date.now() + 60 * 60 * 1000),
      isAllDay: false,
      location: '',
      color: '#6200EE',
    });
    setShowAddModal(false);
  };

  const handleDeleteEvent = (id: string) => {
    Alert.alert(
      'Delete Event',
      'Are you sure you want to delete this event?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteEvent(id) },
      ]
    );
  };

  const renderEventItem = ({ item }: { item: CalendarEvent }) => {
    const startTime = formatTime(item.startDate);
    const endTime = formatTime(item.endDate);
    const isToday = new Date(item.startDate).toDateString() === new Date().toDateString();
    
    return (
      <TouchableOpacity
        style={[
          styles.eventItem,
          { 
            backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF',
            borderLeftColor: item.color || '#6200EE',
          },
        ]}
        onPress={() => {
          // TODO: Navigate to event detail/edit screen
          console.log('Open event:', item.id);
        }}
      >
        <View style={styles.eventContent}>
          <View style={styles.eventHeader}>
            <Text
              style={[
                styles.eventTitle,
                { color: isDark ? '#FFFFFF' : '#000000' },
              ]}
            >
              {item.title}
            </Text>
            <TouchableOpacity onPress={() => handleDeleteEvent(item.id)}>
              <Ionicons name="trash-outline" size={16} color="#F44336" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.eventMeta}>
            <View style={styles.eventTime}>
              <Ionicons name="time-outline" size={14} color={isDark ? '#CCCCCC' : '#666666'} />
              <Text style={[styles.eventTimeText, { color: isDark ? '#CCCCCC' : '#666666' }]}>
                {item.isAllDay ? 'All day' : `${startTime} - ${endTime}`}
              </Text>
            </View>
            
            {item.location && (
              <View style={styles.eventLocation}>
                <Ionicons name="location-outline" size={14} color={isDark ? '#CCCCCC' : '#666666'} />
                <Text style={[styles.eventLocationText, { color: isDark ? '#CCCCCC' : '#666666' }]}>
                  {item.location}
                </Text>
              </View>
            )}
          </View>
          
          {item.description && (
            <Text
              style={[
                styles.eventDescription,
                { color: isDark ? '#CCCCCC' : '#666666' },
              ]}
              numberOfLines={2}
            >
              {item.description}
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderAgendaItem = ({ item }: { item: CalendarEvent }) => {
    const eventDate = new Date(item.startDate);
    const isToday = eventDate.toDateString() === new Date().toDateString();
    const isTomorrow = eventDate.toDateString() === new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString();
    
    let dateLabel = formatDate(eventDate, 'MMM dd, yyyy');
    if (isToday) dateLabel = 'Today';
    else if (isTomorrow) dateLabel = 'Tomorrow';
    
    return (
      <View style={styles.agendaItem}>
        <View style={styles.agendaDate}>
          <Text style={[styles.agendaDateText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
            {dateLabel}
          </Text>
        </View>
        {renderEventItem({ item })}
      </View>
    );
  };

  const renderViewModeButton = (mode: ViewMode, icon: string, label: string) => (
    <TouchableOpacity
      style={[
        styles.viewModeButton,
        viewMode === mode && styles.activeViewModeButton,
        { 
          backgroundColor: viewMode === mode 
            ? '#6200EE' 
            : (isDark ? '#1E1E1E' : '#F5F5F5')
        }
      ]}
      onPress={() => setViewMode(mode)}
    >
      <Ionicons 
        name={icon as any} 
        size={16} 
        color={viewMode === mode ? '#FFFFFF' : (isDark ? '#FFFFFF' : '#000000')} 
      />
      <Text
        style={[
          styles.viewModeButtonText,
          { 
            color: viewMode === mode 
              ? '#FFFFFF' 
              : (isDark ? '#FFFFFF' : '#000000')
          }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#121212' : '#F5F5F5' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
          Calendar
        </Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* View Mode Selector */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.viewModeSelector}>
        {renderViewModeButton('month', 'calendar', 'Month')}
        {renderViewModeButton('week', 'calendar-outline', 'Week')}
        {renderViewModeButton('day', 'today', 'Day')}
        {renderViewModeButton('agenda', 'list', 'Agenda')}
      </ScrollView>

      {/* Calendar View */}
      {viewMode === 'month' && (
        <View style={styles.calendarContainer}>
          <Calendar
            current={selectedDate}
            onDayPress={(day) => setSelectedDate(day.dateString)}
            markedDates={markedDates}
            markingType="multi-dot"
            theme={{
              backgroundColor: isDark ? '#121212' : '#FFFFFF',
              calendarBackground: isDark ? '#1E1E1E' : '#FFFFFF',
              textSectionTitleColor: isDark ? '#FFFFFF' : '#000000',
              selectedDayBackgroundColor: '#6200EE',
              selectedDayTextColor: '#FFFFFF',
              todayTextColor: '#6200EE',
              dayTextColor: isDark ? '#FFFFFF' : '#000000',
              textDisabledColor: isDark ? '#666666' : '#CCCCCC',
              dotColor: '#6200EE',
              selectedDotColor: '#FFFFFF',
              arrowColor: '#6200EE',
              monthTextColor: isDark ? '#FFFFFF' : '#000000',
              indicatorColor: '#6200EE',
              textDayFontWeight: '500',
              textMonthFontWeight: 'bold',
              textDayHeaderFontWeight: '600',
            }}
          />
        </View>
      )}

      {/* Selected Date Events */}
      {(viewMode === 'month' || viewMode === 'day') && (
        <View style={styles.eventsSection}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
            {viewMode === 'month' 
              ? `Events for ${formatDate(new Date(selectedDate), 'MMM dd, yyyy')}`
              : 'Today\'s Events'
            }
          </Text>
          
          <FlatList
            data={selectedDateEvents}
            renderItem={renderEventItem}
            keyExtractor={(item) => item.id}
            style={styles.eventsList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="calendar-outline" size={48} color={isDark ? '#666666' : '#CCCCCC'} />
                <Text style={[styles.emptyText, { color: isDark ? '#666666' : '#999999' }]}>
                  No events for this date
                </Text>
              </View>
            }
          />
        </View>
      )}

      {/* Agenda View */}
      {viewMode === 'agenda' && (
        <View style={styles.agendaSection}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
            Upcoming Events
          </Text>
          
          <FlatList
            data={upcomingEvents}
            renderItem={renderAgendaItem}
            keyExtractor={(item) => item.id}
            style={styles.agendaList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="calendar-outline" size={64} color={isDark ? '#666666' : '#CCCCCC'} />
                <Text style={[styles.emptyText, { color: isDark ? '#666666' : '#999999' }]}>
                  No upcoming events
                </Text>
              </View>
            }
          />
        </View>
      )}

      {/* Add Event Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#121212' : '#FFFFFF' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Add Event
            </Text>
            <TouchableOpacity onPress={handleAddEvent}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Title *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter event title"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newEvent.title}
                onChangeText={(text) => setNewEvent({ ...newEvent, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Description
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  styles.multilineInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter description (optional)"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newEvent.description}
                onChangeText={(text) => setNewEvent({ ...newEvent, description: text })}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <View style={styles.switchRow}>
                <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                  All Day
                </Text>
                <Switch
                  value={newEvent.isAllDay}
                  onValueChange={(value) => setNewEvent({ ...newEvent, isAllDay: value })}
                  trackColor={{ false: '#767577', true: '#6200EE' }}
                  thumbColor={newEvent.isAllDay ? '#FFFFFF' : '#f4f3f4'}
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Start Date & Time
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateTimeButton,
                  { backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5' }
                ]}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Ionicons name="calendar" size={20} color={isDark ? '#FFFFFF' : '#000000'} />
                <Text style={[styles.dateTimeText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                  {newEvent.isAllDay 
                    ? formatDate(newEvent.startDate)
                    : formatDateTime(newEvent.startDate)
                  }
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                End Date & Time
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateTimeButton,
                  { backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5' }
                ]}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Ionicons name="calendar" size={20} color={isDark ? '#FFFFFF' : '#000000'} />
                <Text style={[styles.dateTimeText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                  {newEvent.isAllDay 
                    ? formatDate(newEvent.endDate)
                    : formatDateTime(newEvent.endDate)
                  }
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Location
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter location (optional)"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newEvent.location}
                onChangeText={(text) => setNewEvent({ ...newEvent, location: text })}
              />
            </View>
          </ScrollView>

          {/* Date/Time Pickers */}
          {showStartDatePicker && (
            <DateTimePicker
              value={newEvent.startDate}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowStartDatePicker(false);
                if (selectedDate) {
                  setNewEvent({ ...newEvent, startDate: selectedDate });
                  if (!newEvent.isAllDay) {
                    setShowStartTimePicker(true);
                  }
                }
              }}
            />
          )}

          {showStartTimePicker && (
            <DateTimePicker
              value={newEvent.startDate}
              mode="time"
              display="default"
              onChange={(event, selectedTime) => {
                setShowStartTimePicker(false);
                if (selectedTime) {
                  setNewEvent({ ...newEvent, startDate: selectedTime });
                }
              }}
            />
          )}

          {showEndDatePicker && (
            <DateTimePicker
              value={newEvent.endDate}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowEndDatePicker(false);
                if (selectedDate) {
                  setNewEvent({ ...newEvent, endDate: selectedDate });
                  if (!newEvent.isAllDay) {
                    setShowEndTimePicker(true);
                  }
                }
              }}
            />
          )}

          {showEndTimePicker && (
            <DateTimePicker
              value={newEvent.endDate}
              mode="time"
              display="default"
              onChange={(event, selectedTime) => {
                setShowEndTimePicker(false);
                if (selectedTime) {
                  setNewEvent({ ...newEvent, endDate: selectedTime });
                }
              }}
            />
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  addButton: {
    backgroundColor: '#6200EE',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    ...getShadowStyle('#6200EE', 0.3, 8, 4),
  },
  viewModeSelector: {
    paddingHorizontal: 24,
    marginBottom: 20,
  },
  viewModeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 10,
    gap: 8,
    ...getShadowStyle('#000', 0.05, 4, 1),
  },
  activeViewModeButton: {
    backgroundColor: '#6200EE',
    ...getShadowStyle('#6200EE', 0.2, 6, 3),
  },
  viewModeButtonText: {
    fontSize: 15,
    fontWeight: '600',
  },
  calendarContainer: {
    marginHorizontal: 24,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    ...getShadowStyle('#000', 0.12, 8, 3),
  },
  eventsSection: {
    flex: 1,
    paddingHorizontal: 24,
  },
  agendaSection: {
    flex: 1,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  eventsList: {
    flex: 1,
  },
  agendaList: {
    flex: 1,
  },
  eventItem: {
    borderRadius: 16,
    marginBottom: 12,
    padding: 20,
    borderLeftWidth: 5,
    ...getShadowStyle('#000', 0.12, 8, 3),
  },
  eventContent: {
    flex: 1,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  eventTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    marginRight: 12,
    lineHeight: 24,
  },
  eventMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  eventTime: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  eventTimeText: {
    fontSize: 15,
    fontWeight: '500',
  },
  eventLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  eventLocationText: {
    fontSize: 15,
    fontWeight: '500',
  },
  eventDescription: {
    fontSize: 15,
    lineHeight: 22,
    marginTop: 8,
    opacity: 0.8,
  },
  agendaItem: {
    marginBottom: 20,
  },
  agendaDate: {
    marginBottom: 12,
  },
  agendaDateText: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 26,
    opacity: 0.7,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  modalButton: {
    fontSize: 17,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  inputGroup: {
    marginBottom: 28,
  },
  inputLabel: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 10,
    letterSpacing: -0.2,
  },
  textInput: {
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 16,
    fontSize: 16,
    fontWeight: '400',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  dateTimeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 16,
    borderRadius: 16,
    gap: 14,
    ...getShadowStyle('#000', 0.05, 4, 1),
  },
  dateTimeText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
