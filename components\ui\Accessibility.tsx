import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  AccessibilityInfo,
  findNodeHandle,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useSettingsStore } from '@/store';

// Accessible touchable component with proper feedback
interface AccessibleTouchableProps {
  children: React.ReactNode;
  onPress: () => void;
  accessibilityLabel: string;
  accessibilityHint?: string;
  accessibilityRole?: 'button' | 'link' | 'tab' | 'menuitem' | 'switch';
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean | 'mixed';
    busy?: boolean;
    expanded?: boolean;
  };
  style?: ViewStyle;
  disabled?: boolean;
  testID?: string;
}

export function AccessibleTouchable({
  children,
  onPress,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  accessibilityState,
  style,
  disabled = false,
  testID,
}: AccessibleTouchableProps) {
  const { settings } = useSettingsStore();

  const handlePress = () => {
    if (!disabled) {
      // Provide haptic feedback if enabled
      if (settings.notifications.vibrationEnabled) {
        // Note: You might want to use Haptics.impactAsync() here
      }
      onPress();
    }
  };

  return (
    <TouchableOpacity
      style={style}
      onPress={handlePress}
      disabled={disabled}
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole={accessibilityRole}
      accessibilityState={{
        disabled,
        ...accessibilityState,
      }}
      testID={testID}
      activeOpacity={0.7}
    >
      {children}
    </TouchableOpacity>
  );
}

// Accessible text component with proper contrast and sizing
interface AccessibleTextProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption';
  color?: 'primary' | 'secondary' | 'disabled' | 'hint';
  style?: TextStyle;
  accessibilityLabel?: string;
  accessibilityRole?: 'header' | 'text' | 'summary';
  numberOfLines?: number;
}

export function AccessibleText({
  children,
  variant = 'body1',
  color = 'primary',
  style,
  accessibilityLabel,
  accessibilityRole = 'text',
  numberOfLines,
}: AccessibleTextProps) {
  const { theme } = useTheme();
  const { settings } = useSettingsStore();

  const getTextStyle = (): TextStyle => {
    const typography = theme.typography[variant];
    const textColor = theme.colors.text[color];

    return {
      fontSize: typography.fontSize,
      fontWeight: typography.fontWeight as any,
      lineHeight: typography.lineHeight,
      color: textColor,
    };
  };

  return (
    <Text
      style={[getTextStyle(), style]}
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole={accessibilityRole}
      numberOfLines={numberOfLines}
      adjustsFontSizeToFit={settings.accessibility.fontSize === 'extra-large'}
      minimumFontScale={0.8}
    >
      {children}
    </Text>
  );
}

// Screen reader announcement component
interface AnnouncementProps {
  message: string;
  priority?: 'low' | 'high';
}

export function Announcement({ message, priority = 'low' }: AnnouncementProps) {
  React.useEffect(() => {
    if (message) {
      const announcement = priority === 'high' ? message : message;
      AccessibilityInfo.announceForAccessibility(announcement);
    }
  }, [message, priority]);

  return null; // This component doesn't render anything
}

// Focus management hook
export function useFocusManagement() {
  const focusElement = (ref: React.RefObject<any>) => {
    if (ref.current) {
      const node = findNodeHandle(ref.current);
      if (node) {
        AccessibilityInfo.setAccessibilityFocus(node);
      }
    }
  };

  const announceForScreenReader = (message: string) => {
    AccessibilityInfo.announceForAccessibility(message);
  };

  return {
    focusElement,
    announceForScreenReader,
  };
}

// Skip link component for keyboard navigation
interface SkipLinkProps {
  targetRef: React.RefObject<any>;
  label: string;
}

export function SkipLink({ targetRef, label }: SkipLinkProps) {
  const { theme } = useTheme();
  const { focusElement } = useFocusManagement();

  const handleSkip = () => {
    focusElement(targetRef);
  };

  return (
    <TouchableOpacity
      style={[
        styles.skipLink,
        {
          backgroundColor: theme.colors.primary,
          color: theme.colors.onPrimary,
        },
      ]}
      onPress={handleSkip}
      accessible={true}
      accessibilityLabel={label}
      accessibilityRole="button"
    >
      <Text style={[styles.skipLinkText, { color: theme.colors.onPrimary }]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
}

// High contrast wrapper
interface HighContrastWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function HighContrastWrapper({ children, style }: HighContrastWrapperProps) {
  const { theme } = useTheme();
  const { settings } = useSettingsStore();

  if (!settings.accessibility.highContrast) {
    return <>{children}</>;
  }

  return (
    <View
      style={[
        {
          borderWidth: 1,
          borderColor: theme.colors.border.primary,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
}

// Reduced motion wrapper
interface ReducedMotionWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ReducedMotionWrapper({ children, fallback }: ReducedMotionWrapperProps) {
  const { settings } = useSettingsStore();

  if (settings.accessibility.reduceMotion && fallback) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Accessibility info hook
export function useAccessibilityInfo() {
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = React.useState(false);
  const [isReduceMotionEnabled, setIsReduceMotionEnabled] = React.useState(false);
  const [isReduceTransparencyEnabled, setIsReduceTransparencyEnabled] = React.useState(false);

  React.useEffect(() => {
    // Check initial accessibility settings
    AccessibilityInfo.isScreenReaderEnabled().then(setIsScreenReaderEnabled);
    AccessibilityInfo.isReduceMotionEnabled().then(setIsReduceMotionEnabled);
    AccessibilityInfo.isReduceTransparencyEnabled().then(setIsReduceTransparencyEnabled);

    // Listen for changes
    const screenReaderSubscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      setIsScreenReaderEnabled
    );

    const reduceMotionSubscription = AccessibilityInfo.addEventListener(
      'reduceMotionChanged',
      setIsReduceMotionEnabled
    );

    const reduceTransparencySubscription = AccessibilityInfo.addEventListener(
      'reduceTransparencyChanged',
      setIsReduceTransparencyEnabled
    );

    return () => {
      screenReaderSubscription?.remove();
      reduceMotionSubscription?.remove();
      reduceTransparencySubscription?.remove();
    };
  }, []);

  return {
    isScreenReaderEnabled,
    isReduceMotionEnabled,
    isReduceTransparencyEnabled,
  };
}

// Semantic heading component
interface HeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  children: React.ReactNode;
  style?: TextStyle;
}

export function Heading({ level, children, style }: HeadingProps) {
  const variant = `h${level}` as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

  return (
    <AccessibleText
      variant={variant}
      accessibilityRole="header"
      style={style}
    >
      {children}
    </AccessibleText>
  );
}

// Live region for dynamic content updates
interface LiveRegionProps {
  children: React.ReactNode;
  politeness?: 'polite' | 'assertive';
  atomic?: boolean;
}

export function LiveRegion({ 
  children, 
  politeness = 'polite',
  atomic = false 
}: LiveRegionProps) {
  return (
    <View
      accessible={true}
      accessibilityLiveRegion={politeness}
      accessibilityAtomic={atomic}
    >
      {children}
    </View>
  );
}

// Form field wrapper with proper labeling
interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  error?: string;
  hint?: string;
  required?: boolean;
  style?: ViewStyle;
}

export function FormField({
  label,
  children,
  error,
  hint,
  required = false,
  style,
}: FormFieldProps) {
  const { theme } = useTheme();
  const fieldId = React.useId();

  return (
    <View style={style}>
      <AccessibleText
        variant="body2"
        color="primary"
        accessibilityRole="text"
        style={{ marginBottom: 8 }}
      >
        {label}
        {required && <Text style={{ color: theme.colors.error }}> *</Text>}
      </AccessibleText>
      
      <View
        accessible={false}
        accessibilityElementsHidden={false}
      >
        {children}
      </View>
      
      {hint && !error && (
        <AccessibleText
          variant="caption"
          color="hint"
          style={{ marginTop: 4 }}
        >
          {hint}
        </AccessibleText>
      )}
      
      {error && (
        <LiveRegion politeness="assertive">
          <AccessibleText
            variant="caption"
            style={{ color: theme.colors.error, marginTop: 4 }}
          >
            {error}
          </AccessibleText>
        </LiveRegion>
      )}
    </View>
  );
}

const styles = {
  skipLink: {
    position: 'absolute' as const,
    top: -1000,
    left: 0,
    padding: 8,
    zIndex: 9999,
  },
  skipLinkText: {
    fontSize: 16,
    fontWeight: 'bold' as const,
  },
};
