import { format, isToday, isTomorrow, isYesterday, differenceInDays, addDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { Priority, TaskStatus, RecurrenceType } from '@/types';

// Date utility functions
export const formatDate = (date: Date | string, formatString: string = 'MMM dd, yyyy'): string => {
  return format(new Date(date), formatString);
};

export const formatTime = (date: Date | string): string => {
  return format(new Date(date), 'HH:mm');
};

export const formatDateTime = (date: Date | string): string => {
  return format(new Date(date), 'MMM dd, yyyy HH:mm');
};

export const getRelativeDate = (date: Date | string): string => {
  const targetDate = new Date(date);
  
  if (isToday(targetDate)) return 'Today';
  if (isTomorrow(targetDate)) return 'Tomorrow';
  if (isYesterday(targetDate)) return 'Yesterday';
  
  const daysDiff = differenceInDays(targetDate, new Date());
  
  if (daysDiff > 0 && daysDiff <= 7) {
    return `In ${daysDiff} day${daysDiff > 1 ? 's' : ''}`;
  }
  
  if (daysDiff < 0 && daysDiff >= -7) {
    return `${Math.abs(daysDiff)} day${Math.abs(daysDiff) > 1 ? 's' : ''} ago`;
  }
  
  return formatDate(targetDate);
};

export const isOverdue = (date: Date | string): boolean => {
  return new Date(date) < new Date() && !isToday(new Date(date));
};

export const getWeekRange = (date: Date = new Date()) => {
  return {
    start: startOfWeek(date, { weekStartsOn: 1 }), // Monday
    end: endOfWeek(date, { weekStartsOn: 1 })
  };
};

export const getMonthRange = (date: Date = new Date()) => {
  return {
    start: startOfMonth(date),
    end: endOfMonth(date)
  };
};

// Priority utility functions
export const getPriorityColor = (priority: Priority): string => {
  const colors = {
    low: '#4CAF50',      // Green
    medium: '#FF9800',   // Orange
    high: '#F44336',     // Red
    urgent: '#9C27B0'    // Purple
  };
  return colors[priority];
};

export const getPriorityWeight = (priority: Priority): number => {
  const weights = {
    low: 1,
    medium: 2,
    high: 3,
    urgent: 4
  };
  return weights[priority];
};

export const sortByPriority = <T extends { priority: Priority }>(items: T[]): T[] => {
  return items.sort((a, b) => getPriorityWeight(b.priority) - getPriorityWeight(a.priority));
};

// Status utility functions
export const getStatusColor = (status: TaskStatus): string => {
  const colors = {
    pending: '#2196F3',    // Blue
    completed: '#4CAF50',  // Green
    cancelled: '#9E9E9E'   // Gray
  };
  return colors[status];
};

// ID generation
export const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// Text utility functions
export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

export const highlightSearchTerm = (text: string, searchTerm: string): string => {
  if (!searchTerm) return text;
  
  const regex = new RegExp(`(${searchTerm})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
};

export const extractHashtags = (text: string): string[] => {
  const hashtagRegex = /#[\w]+/g;
  const matches = text.match(hashtagRegex);
  return matches ? matches.map(tag => tag.substring(1)) : [];
};

// Validation functions
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Recurrence utility functions
export const getNextRecurrenceDate = (
  baseDate: Date,
  recurrenceType: RecurrenceType,
  interval: number = 1
): Date | null => {
  if (recurrenceType === 'none') return null;
  
  const nextDate = new Date(baseDate);
  
  switch (recurrenceType) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + interval);
      break;
    case 'weekly':
      nextDate.setDate(nextDate.getDate() + (7 * interval));
      break;
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + interval);
      break;
    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + interval);
      break;
    default:
      return null;
  }
  
  return nextDate;
};

export const generateRecurrenceDates = (
  startDate: Date,
  recurrenceType: RecurrenceType,
  interval: number = 1,
  endDate?: Date,
  maxOccurrences: number = 100
): Date[] => {
  if (recurrenceType === 'none') return [startDate];
  
  const dates: Date[] = [startDate];
  let currentDate = new Date(startDate);
  let count = 1;
  
  while (count < maxOccurrences) {
    const nextDate = getNextRecurrenceDate(currentDate, recurrenceType, interval);
    if (!nextDate) break;
    
    if (endDate && nextDate > endDate) break;
    
    dates.push(nextDate);
    currentDate = nextDate;
    count++;
  }
  
  return dates;
};

// Color utility functions
export const hexToRgba = (hex: string, alpha: number = 1): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

export const getContrastColor = (backgroundColor: string): string => {
  // Remove # if present
  const hex = backgroundColor.replace('#', '');
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return black or white based on luminance
  return luminance > 0.5 ? '#000000' : '#FFFFFF';
};

// Array utility functions
export const groupBy = <T, K extends keyof any>(
  array: T[],
  getKey: (item: T) => K
): Record<K, T[]> => {
  return array.reduce((groups, item) => {
    const key = getKey(item);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {} as Record<K, T[]>);
};

export const sortBy = <T>(
  array: T[],
  getKey: (item: T) => any,
  direction: 'asc' | 'desc' = 'asc'
): T[] => {
  return array.sort((a, b) => {
    const aKey = getKey(a);
    const bKey = getKey(b);
    
    if (aKey < bKey) return direction === 'asc' ? -1 : 1;
    if (aKey > bKey) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

// Storage utility functions
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Debounce function for search
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Theme utility functions
export const getThemeColors = (isDark: boolean) => {
  return {
    primary: isDark ? '#BB86FC' : '#6200EE',
    primaryVariant: isDark ? '#3700B3' : '#3700B3',
    secondary: isDark ? '#03DAC6' : '#03DAC6',
    background: isDark ? '#121212' : '#FFFFFF',
    surface: isDark ? '#1E1E1E' : '#FFFFFF',
    error: isDark ? '#CF6679' : '#B00020',
    onPrimary: isDark ? '#000000' : '#FFFFFF',
    onSecondary: isDark ? '#000000' : '#000000',
    onBackground: isDark ? '#FFFFFF' : '#000000',
    onSurface: isDark ? '#FFFFFF' : '#000000',
    onError: isDark ? '#000000' : '#FFFFFF',
  };
};
