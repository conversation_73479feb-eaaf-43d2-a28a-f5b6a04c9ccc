import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTodoStore } from '@/store';
import { TodoItem, Priority, TaskStatus } from '@/types';
import { formatDate, getRelativeDate, getPriorityColor, isOverdue } from '@/utils';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function TodosScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const {
    todos,
    categories,
    addTodo,
    updateTodo,
    deleteTodo,
    toggleTodoStatus,
    addCategory,
  } = useTodoStore();

  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedPriority, setSelectedPriority] = useState<Priority | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [newTodo, setNewTodo] = useState({
    title: '',
    description: '',
    category: '',
    priority: 'medium' as Priority,
    dueDate: undefined as Date | undefined,
  });

  // Filter and sort todos
  const filteredTodos = useMemo(() => {
    let filtered = todos.filter((todo) => {
      const matchesSearch = todo.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           todo.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = !selectedCategory || todo.category === selectedCategory;
      const matchesPriority = !selectedPriority || todo.priority === selectedPriority;
      
      return matchesSearch && matchesCategory && matchesPriority;
    });

    // Sort by priority and due date
    return filtered.sort((a, b) => {
      // Completed items go to bottom
      if (a.status === 'completed' && b.status !== 'completed') return 1;
      if (b.status === 'completed' && a.status !== 'completed') return -1;
      
      // Overdue items go to top
      const aOverdue = a.dueDate && isOverdue(a.dueDate);
      const bOverdue = b.dueDate && isOverdue(b.dueDate);
      if (aOverdue && !bOverdue) return -1;
      if (bOverdue && !aOverdue) return 1;
      
      // Sort by due date
      if (a.dueDate && b.dueDate) {
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      }
      if (a.dueDate && !b.dueDate) return -1;
      if (!a.dueDate && b.dueDate) return 1;
      
      // Finally sort by priority
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, [todos, searchQuery, selectedCategory, selectedPriority]);

  const handleAddTodo = () => {
    if (!newTodo.title.trim()) {
      Alert.alert('Error', 'Please enter a title for the todo');
      return;
    }

    addTodo({
      title: newTodo.title.trim(),
      description: newTodo.description.trim() || undefined,
      category: newTodo.category || undefined,
      priority: newTodo.priority,
      dueDate: newTodo.dueDate,
      status: 'pending',
      tags: [],
      subtasks: [],
      recurrence: { type: 'none' },
    });

    // Reset form
    setNewTodo({
      title: '',
      description: '',
      category: '',
      priority: 'medium',
      dueDate: undefined,
    });
    setShowAddModal(false);
  };

  const handleDeleteTodo = (id: string) => {
    Alert.alert(
      'Delete Todo',
      'Are you sure you want to delete this todo?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteTodo(id) },
      ]
    );
  };

  const renderTodoItem = ({ item }: { item: TodoItem }) => {
    const isCompleted = item.status === 'completed';
    const overdue = item.dueDate && isOverdue(item.dueDate);
    
    return (
      <TouchableOpacity
        style={[
          styles.todoItem,
          { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
          isCompleted && styles.completedTodo,
        ]}
        onPress={() => toggleTodoStatus(item.id)}
      >
        <View style={styles.todoHeader}>
          <TouchableOpacity
            style={[
              styles.checkbox,
              isCompleted && styles.checkedBox,
              { borderColor: getPriorityColor(item.priority) },
            ]}
            onPress={() => toggleTodoStatus(item.id)}
          >
            {isCompleted && (
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            )}
          </TouchableOpacity>
          
          <View style={styles.todoContent}>
            <Text
              style={[
                styles.todoTitle,
                { color: isDark ? '#FFFFFF' : '#000000' },
                isCompleted && styles.completedText,
              ]}
            >
              {item.title}
            </Text>
            
            {item.description && (
              <Text
                style={[
                  styles.todoDescription,
                  { color: isDark ? '#CCCCCC' : '#666666' },
                  isCompleted && styles.completedText,
                ]}
              >
                {item.description}
              </Text>
            )}
            
            <View style={styles.todoMeta}>
              {item.category && (
                <View style={[styles.categoryTag, { backgroundColor: getPriorityColor(item.priority) + '20' }]}>
                  <Text style={[styles.categoryText, { color: getPriorityColor(item.priority) }]}>
                    {item.category}
                  </Text>
                </View>
              )}
              
              {item.dueDate && (
                <Text
                  style={[
                    styles.dueDate,
                    { color: overdue ? '#F44336' : (isDark ? '#CCCCCC' : '#666666') },
                  ]}
                >
                  {getRelativeDate(item.dueDate)}
                </Text>
              )}
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteTodo(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderFilterChip = (label: string, isSelected: boolean, onPress: () => void) => (
    <TouchableOpacity
      style={[
        styles.filterChip,
        isSelected && styles.selectedFilterChip,
        { backgroundColor: isSelected ? '#6200EE' : (isDark ? '#2E2E2E' : '#F5F5F5') },
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.filterChipText,
          { color: isSelected ? '#FFFFFF' : (isDark ? '#FFFFFF' : '#000000') },
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#121212' : '#F5F5F5' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
          To-dos
        </Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' }]}>
        <Ionicons name="search" size={20} color={isDark ? '#CCCCCC' : '#666666'} />
        <TextInput
          style={[styles.searchInput, { color: isDark ? '#FFFFFF' : '#000000' }]}
          placeholder="Search todos..."
          placeholderTextColor={isDark ? '#666666' : '#999999'}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Filter Chips */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
        {renderFilterChip('All', !selectedCategory && !selectedPriority, () => {
          setSelectedCategory(null);
          setSelectedPriority(null);
        })}
        
        {categories.map((category) =>
          renderFilterChip(
            category,
            selectedCategory === category,
            () => setSelectedCategory(selectedCategory === category ? null : category)
          )
        )}
        
        {(['urgent', 'high', 'medium', 'low'] as Priority[]).map((priority) =>
          renderFilterChip(
            priority.charAt(0).toUpperCase() + priority.slice(1),
            selectedPriority === priority,
            () => setSelectedPriority(selectedPriority === priority ? null : priority)
          )
        )}
      </ScrollView>

      {/* Todo List */}
      <FlatList
        data={filteredTodos}
        renderItem={renderTodoItem}
        keyExtractor={(item) => item.id}
        style={styles.todoList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="checkmark-circle-outline" size={64} color={isDark ? '#666666' : '#CCCCCC'} />
            <Text style={[styles.emptyText, { color: isDark ? '#666666' : '#999999' }]}>
              {searchQuery || selectedCategory || selectedPriority
                ? 'No todos match your filters'
                : 'No todos yet. Add one to get started!'}
            </Text>
          </View>
        }
      />

      {/* Add Todo Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#121212' : '#FFFFFF' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Add Todo
            </Text>
            <TouchableOpacity onPress={handleAddTodo}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Title *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter todo title"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newTodo.title}
                onChangeText={(text) => setNewTodo({ ...newTodo, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Description
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  styles.multilineInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter description (optional)"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newTodo.description}
                onChangeText={(text) => setNewTodo({ ...newTodo, description: text })}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Category
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.categorySelector}>
                  {categories.map((category) => (
                    <TouchableOpacity
                      key={category}
                      style={[
                        styles.categoryOption,
                        newTodo.category === category && styles.selectedCategoryOption,
                        { 
                          backgroundColor: newTodo.category === category 
                            ? '#6200EE' 
                            : (isDark ? '#1E1E1E' : '#F5F5F5')
                        }
                      ]}
                      onPress={() => setNewTodo({ 
                        ...newTodo, 
                        category: newTodo.category === category ? '' : category 
                      })}
                    >
                      <Text
                        style={[
                          styles.categoryOptionText,
                          { 
                            color: newTodo.category === category 
                              ? '#FFFFFF' 
                              : (isDark ? '#FFFFFF' : '#000000')
                          }
                        ]}
                      >
                        {category}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Priority
              </Text>
              <View style={styles.prioritySelector}>
                {(['low', 'medium', 'high', 'urgent'] as Priority[]).map((priority) => (
                  <TouchableOpacity
                    key={priority}
                    style={[
                      styles.priorityOption,
                      newTodo.priority === priority && styles.selectedPriorityOption,
                      { 
                        backgroundColor: newTodo.priority === priority 
                          ? getPriorityColor(priority)
                          : (isDark ? '#1E1E1E' : '#F5F5F5')
                      }
                    ]}
                    onPress={() => setNewTodo({ ...newTodo, priority })}
                  >
                    <Text
                      style={[
                        styles.priorityOptionText,
                        { 
                          color: newTodo.priority === priority 
                            ? '#FFFFFF' 
                            : (isDark ? '#FFFFFF' : '#000000')
                        }
                      ]}
                    >
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    backgroundColor: '#6200EE',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  filtersContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  selectedFilterChip: {
    backgroundColor: '#6200EE',
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  todoList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  todoItem: {
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  completedTodo: {
    opacity: 0.6,
  },
  todoHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  checkedBox: {
    backgroundColor: '#6200EE',
    borderColor: '#6200EE',
  },
  todoContent: {
    flex: 1,
  },
  todoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  todoDescription: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  completedText: {
    textDecorationLine: 'line-through',
  },
  todoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  categoryTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  dueDate: {
    fontSize: 12,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  categorySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  categoryOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  selectedCategoryOption: {
    backgroundColor: '#6200EE',
  },
  categoryOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityOption: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  selectedPriorityOption: {
    // Color set dynamically
  },
  priorityOptionText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
