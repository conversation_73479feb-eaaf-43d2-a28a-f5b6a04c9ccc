import { useColorScheme } from '@/hooks/useColorScheme';
import { useTodoStore } from '@/store';
import { Priority, TodoItem } from '@/types';
import { getPriorityColor, getRelativeDate, isOverdue } from '@/utils';
import { Ionicons } from '@expo/vector-icons';
import React, { useMemo, useState } from 'react';
import {
    Alert,
    FlatList,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function TodosScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const {
    todos,
    categories,
    addTodo,
    updateTodo,
    deleteTodo,
    toggleTodoStatus,
    addCategory,
  } = useTodoStore();

  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedPriority, setSelectedPriority] = useState<Priority | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [newTodo, setNewTodo] = useState({
    title: '',
    description: '',
    category: '',
    priority: 'medium' as Priority,
    dueDate: undefined as Date | undefined,
  });

  // Filter and sort todos
  const filteredTodos = useMemo(() => {
    let filtered = todos.filter((todo) => {
      const matchesSearch = todo.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           todo.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = !selectedCategory || todo.category === selectedCategory;
      const matchesPriority = !selectedPriority || todo.priority === selectedPriority;
      
      return matchesSearch && matchesCategory && matchesPriority;
    });

    // Sort by priority and due date
    return filtered.sort((a, b) => {
      // Completed items go to bottom
      if (a.status === 'completed' && b.status !== 'completed') return 1;
      if (b.status === 'completed' && a.status !== 'completed') return -1;
      
      // Overdue items go to top
      const aOverdue = a.dueDate && isOverdue(a.dueDate);
      const bOverdue = b.dueDate && isOverdue(b.dueDate);
      if (aOverdue && !bOverdue) return -1;
      if (bOverdue && !aOverdue) return 1;
      
      // Sort by due date
      if (a.dueDate && b.dueDate) {
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      }
      if (a.dueDate && !b.dueDate) return -1;
      if (!a.dueDate && b.dueDate) return 1;
      
      // Finally sort by priority
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, [todos, searchQuery, selectedCategory, selectedPriority]);

  const handleAddTodo = () => {
    if (!newTodo.title.trim()) {
      Alert.alert('Error', 'Please enter a title for the todo');
      return;
    }

    addTodo({
      title: newTodo.title.trim(),
      description: newTodo.description.trim() || undefined,
      category: newTodo.category || undefined,
      priority: newTodo.priority,
      dueDate: newTodo.dueDate,
      status: 'pending',
      tags: [],
      subtasks: [],
      recurrence: { type: 'none' },
    });

    // Reset form
    setNewTodo({
      title: '',
      description: '',
      category: '',
      priority: 'medium',
      dueDate: undefined,
    });
    setShowAddModal(false);
  };

  const handleDeleteTodo = (id: string) => {
    Alert.alert(
      'Delete Todo',
      'Are you sure you want to delete this todo?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteTodo(id) },
      ]
    );
  };

  const renderTodoItem = ({ item }: { item: TodoItem }) => {
    const isCompleted = item.status === 'completed';
    const overdue = item.dueDate && isOverdue(item.dueDate);
    
    return (
      <TouchableOpacity
        style={[
          styles.todoItem,
          { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
          isCompleted && styles.completedTodo,
        ]}
        onPress={() => toggleTodoStatus(item.id)}
      >
        <View style={styles.todoHeader}>
          <TouchableOpacity
            style={[
              styles.checkbox,
              isCompleted && styles.checkedBox,
              { borderColor: getPriorityColor(item.priority) },
            ]}
            onPress={() => toggleTodoStatus(item.id)}
          >
            {isCompleted && (
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            )}
          </TouchableOpacity>
          
          <View style={styles.todoContent}>
            <Text
              style={[
                styles.todoTitle,
                { color: isDark ? '#FFFFFF' : '#000000' },
                isCompleted && styles.completedText,
              ]}
            >
              {item.title}
            </Text>
            
            {item.description && (
              <Text
                style={[
                  styles.todoDescription,
                  { color: isDark ? '#CCCCCC' : '#666666' },
                  isCompleted && styles.completedText,
                ]}
              >
                {item.description}
              </Text>
            )}
            
            <View style={styles.todoMeta}>
              {item.category && (
                <View style={[styles.categoryTag, { backgroundColor: getPriorityColor(item.priority) + '20' }]}>
                  <Text style={[styles.categoryText, { color: getPriorityColor(item.priority) }]}>
                    {item.category}
                  </Text>
                </View>
              )}
              
              {item.dueDate && (
                <Text
                  style={[
                    styles.dueDate,
                    { color: overdue ? '#F44336' : (isDark ? '#CCCCCC' : '#666666') },
                  ]}
                >
                  {getRelativeDate(item.dueDate)}
                </Text>
              )}
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteTodo(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderFilterChip = (label: string, isSelected: boolean, onPress: () => void) => (
    <TouchableOpacity
      style={[
        styles.filterChip,
        isSelected && styles.selectedFilterChip,
        { backgroundColor: isSelected ? '#6200EE' : (isDark ? '#2E2E2E' : '#F5F5F5') },
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.filterChipText,
          { color: isSelected ? '#FFFFFF' : (isDark ? '#FFFFFF' : '#000000') },
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#121212' : '#F5F5F5' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
          To-dos
        </Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' }]}>
        <Ionicons name="search" size={20} color={isDark ? '#CCCCCC' : '#666666'} />
        <TextInput
          style={[styles.searchInput, { color: isDark ? '#FFFFFF' : '#000000' }]}
          placeholder="Search todos..."
          placeholderTextColor={isDark ? '#666666' : '#999999'}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Filter Chips */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
        {renderFilterChip('All', !selectedCategory && !selectedPriority, () => {
          setSelectedCategory(null);
          setSelectedPriority(null);
        })}
        
        {categories.map((category) =>
          renderFilterChip(
            category,
            selectedCategory === category,
            () => setSelectedCategory(selectedCategory === category ? null : category)
          )
        )}
        
        {(['urgent', 'high', 'medium', 'low'] as Priority[]).map((priority) =>
          renderFilterChip(
            priority.charAt(0).toUpperCase() + priority.slice(1),
            selectedPriority === priority,
            () => setSelectedPriority(selectedPriority === priority ? null : priority)
          )
        )}
      </ScrollView>

      {/* Todo List */}
      <FlatList
        data={filteredTodos}
        renderItem={renderTodoItem}
        keyExtractor={(item) => item.id}
        style={styles.todoList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="checkmark-circle-outline" size={64} color={isDark ? '#666666' : '#CCCCCC'} />
            <Text style={[styles.emptyText, { color: isDark ? '#666666' : '#999999' }]}>
              {searchQuery || selectedCategory || selectedPriority
                ? 'No todos match your filters'
                : 'No todos yet. Add one to get started!'}
            </Text>
          </View>
        }
      />

      {/* Add Todo Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#121212' : '#FFFFFF' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Add Todo
            </Text>
            <TouchableOpacity onPress={handleAddTodo}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Title *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter todo title"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newTodo.title}
                onChangeText={(text) => setNewTodo({ ...newTodo, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Description
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  styles.multilineInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter description (optional)"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newTodo.description}
                onChangeText={(text) => setNewTodo({ ...newTodo, description: text })}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Category
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.categorySelector}>
                  {categories.map((category) => (
                    <TouchableOpacity
                      key={category}
                      style={[
                        styles.categoryOption,
                        newTodo.category === category && styles.selectedCategoryOption,
                        { 
                          backgroundColor: newTodo.category === category 
                            ? '#6200EE' 
                            : (isDark ? '#1E1E1E' : '#F5F5F5')
                        }
                      ]}
                      onPress={() => setNewTodo({ 
                        ...newTodo, 
                        category: newTodo.category === category ? '' : category 
                      })}
                    >
                      <Text
                        style={[
                          styles.categoryOptionText,
                          { 
                            color: newTodo.category === category 
                              ? '#FFFFFF' 
                              : (isDark ? '#FFFFFF' : '#000000')
                          }
                        ]}
                      >
                        {category}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Priority
              </Text>
              <View style={styles.prioritySelector}>
                {(['low', 'medium', 'high', 'urgent'] as Priority[]).map((priority) => (
                  <TouchableOpacity
                    key={priority}
                    style={[
                      styles.priorityOption,
                      newTodo.priority === priority && styles.selectedPriorityOption,
                      { 
                        backgroundColor: newTodo.priority === priority 
                          ? getPriorityColor(priority)
                          : (isDark ? '#1E1E1E' : '#F5F5F5')
                      }
                    ]}
                    onPress={() => setNewTodo({ ...newTodo, priority })}
                  >
                    <Text
                      style={[
                        styles.priorityOptionText,
                        { 
                          color: newTodo.priority === priority 
                            ? '#FFFFFF' 
                            : (isDark ? '#FFFFFF' : '#000000')
                        }
                      ]}
                    >
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  addButton: {
    backgroundColor: '#6200EE',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#6200EE',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 24,
    marginBottom: 20,
    paddingHorizontal: 18,
    paddingVertical: 14,
    borderRadius: 16,
    gap: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
  },
  filtersContainer: {
    paddingHorizontal: 24,
    marginBottom: 20,
  },
  filterChip: {
    paddingHorizontal: 18,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  selectedFilterChip: {
    backgroundColor: '#6200EE',
    elevation: 3,
    shadowColor: '#6200EE',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '600',
  },
  todoList: {
    flex: 1,
    paddingHorizontal: 24,
  },
  todoItem: {
    borderRadius: 16,
    marginBottom: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
  },
  completedTodo: {
    opacity: 0.7,
    transform: [{ scale: 0.98 }],
  },
  todoHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  checkbox: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 2.5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  checkedBox: {
    backgroundColor: '#6200EE',
    borderColor: '#6200EE',
    transform: [{ scale: 1.1 }],
  },
  todoContent: {
    flex: 1,
  },
  todoTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 6,
    lineHeight: 24,
  },
  todoDescription: {
    fontSize: 15,
    marginBottom: 12,
    lineHeight: 22,
    opacity: 0.8,
  },
  completedText: {
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  todoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flexWrap: 'wrap',
  },
  categoryTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 13,
    fontWeight: '600',
  },
  dueDate: {
    fontSize: 13,
    fontWeight: '600',
  },
  deleteButton: {
    padding: 8,
    borderRadius: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 26,
    opacity: 0.7,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  modalButton: {
    fontSize: 17,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  inputGroup: {
    marginBottom: 28,
  },
  inputLabel: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 10,
    letterSpacing: -0.2,
  },
  textInput: {
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 16,
    fontSize: 16,
    fontWeight: '400',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  categorySelector: {
    flexDirection: 'row',
    gap: 12,
  },
  categoryOption: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 24,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  selectedCategoryOption: {
    backgroundColor: '#6200EE',
    elevation: 3,
    shadowColor: '#6200EE',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  categoryOptionText: {
    fontSize: 15,
    fontWeight: '600',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 12,
  },
  priorityOption: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  selectedPriorityOption: {
    elevation: 3,
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  priorityOptionText: {
    fontSize: 15,
    fontWeight: '600',
  },
});
