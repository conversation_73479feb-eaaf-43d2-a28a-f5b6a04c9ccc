import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNoteStore } from '@/store';
import { Note, Folder } from '@/types';
import { formatDate, truncateText } from '@/utils';
import { useColorScheme } from '@/hooks/useColorScheme';

const NOTE_COLORS = [
  '#FFFFFF', // White
  '#FFF9C4', // Light Yellow
  '#F3E5F5', // Light Purple
  '#E8F5E8', // Light Green
  '#E3F2FD', // Light Blue
  '#FCE4EC', // Light Pink
  '#FFF3E0', // Light Orange
  '#F1F8E9', // Light Lime
];

export default function NotesScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const {
    notes,
    folders,
    addNote,
    updateNote,
    deleteNote,
    addFolder,
    deleteFolder,
    getNotesByFolder,
    searchNotes,
    togglePinNote,
    toggleArchiveNote,
  } = useNoteStore();

  const [showAddModal, setShowAddModal] = useState(false);
  const [showFolderModal, setShowFolderModal] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showArchived, setShowArchived] = useState(false);
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
    color: '#FFFFFF',
    folderId: undefined as string | undefined,
  });
  const [newFolderName, setNewFolderName] = useState('');

  // Filter and sort notes
  const filteredNotes = useMemo(() => {
    let filtered = searchQuery 
      ? searchNotes(searchQuery)
      : selectedFolder 
        ? getNotesByFolder(selectedFolder)
        : notes;

    // Filter by archived status
    filtered = filtered.filter(note => note.isArchived === showArchived);

    // Sort by pinned first, then by updated date
    return filtered.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });
  }, [notes, searchQuery, selectedFolder, showArchived, searchNotes, getNotesByFolder]);

  const handleAddNote = () => {
    if (!newNote.title.trim() && !newNote.content.trim()) {
      Alert.alert('Error', 'Please enter a title or content for the note');
      return;
    }

    addNote({
      title: newNote.title.trim() || 'Untitled',
      content: newNote.content.trim(),
      color: newNote.color,
      folderId: newNote.folderId,
      tags: [],
      attachments: [],
      checklist: [],
      isPinned: false,
      isArchived: false,
    });

    // Reset form
    setNewNote({
      title: '',
      content: '',
      color: '#FFFFFF',
      folderId: undefined,
    });
    setShowAddModal(false);
  };

  const handleAddFolder = () => {
    if (!newFolderName.trim()) {
      Alert.alert('Error', 'Please enter a name for the folder');
      return;
    }

    addFolder({
      name: newFolderName.trim(),
      color: '#6200EE',
    });

    setNewFolderName('');
    setShowFolderModal(false);
  };

  const handleDeleteNote = (id: string) => {
    Alert.alert(
      'Delete Note',
      'Are you sure you want to delete this note?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteNote(id) },
      ]
    );
  };

  const handleDeleteFolder = (id: string) => {
    Alert.alert(
      'Delete Folder',
      'Are you sure you want to delete this folder? Notes in this folder will be moved to the main view.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteFolder(id) },
      ]
    );
  };

  const renderNoteItem = ({ item }: { item: Note }) => {
    const noteColor = isDark ? '#1E1E1E' : item.color || '#FFFFFF';
    const textColor = isDark ? '#FFFFFF' : '#000000';
    
    return (
      <TouchableOpacity
        style={[
          viewMode === 'grid' ? styles.noteItemGrid : styles.noteItemList,
          { backgroundColor: noteColor },
          isDark && { borderColor: '#333333', borderWidth: 1 },
        ]}
        onPress={() => {
          // TODO: Navigate to note detail/edit screen
          console.log('Open note:', item.id);
        }}
      >
        <View style={styles.noteHeader}>
          {item.isPinned && (
            <Ionicons name="pin" size={16} color="#6200EE" />
          )}
          <View style={styles.noteActions}>
            <TouchableOpacity onPress={() => togglePinNote(item.id)}>
              <Ionicons 
                name={item.isPinned ? "pin" : "pin-outline"} 
                size={16} 
                color={isDark ? '#CCCCCC' : '#666666'} 
              />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleDeleteNote(item.id)}>
              <Ionicons name="trash-outline" size={16} color="#F44336" />
            </TouchableOpacity>
          </View>
        </View>
        
        <Text
          style={[
            styles.noteTitle,
            { color: textColor },
          ]}
          numberOfLines={2}
        >
          {item.title}
        </Text>
        
        {item.content && (
          <Text
            style={[
              styles.noteContent,
              { color: isDark ? '#CCCCCC' : '#666666' },
            ]}
            numberOfLines={viewMode === 'grid' ? 4 : 2}
          >
            {truncateText(item.content, viewMode === 'grid' ? 100 : 50)}
          </Text>
        )}
        
        <View style={styles.noteMeta}>
          <Text style={[styles.noteDate, { color: isDark ? '#999999' : '#999999' }]}>
            {formatDate(item.updatedAt, 'MMM dd')}
          </Text>
          
          {item.tags.length > 0 && (
            <View style={styles.tagContainer}>
              {item.tags.slice(0, 2).map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>#{tag}</Text>
                </View>
              ))}
              {item.tags.length > 2 && (
                <Text style={[styles.moreTagsText, { color: isDark ? '#999999' : '#999999' }]}>
                  +{item.tags.length - 2}
                </Text>
              )}
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderFolderItem = ({ item }: { item: Folder }) => (
    <TouchableOpacity
      style={[
        styles.folderItem,
        { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
        selectedFolder === item.id && { borderColor: '#6200EE', borderWidth: 2 },
      ]}
      onPress={() => setSelectedFolder(selectedFolder === item.id ? null : item.id)}
      onLongPress={() => handleDeleteFolder(item.id)}
    >
      <Ionicons name="folder" size={24} color={item.color || '#6200EE'} />
      <Text
        style={[
          styles.folderName,
          { color: isDark ? '#FFFFFF' : '#000000' },
        ]}
        numberOfLines={1}
      >
        {item.name}
      </Text>
      <Text style={[styles.folderCount, { color: isDark ? '#CCCCCC' : '#666666' }]}>
        {getNotesByFolder(item.id).length}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#121212' : '#F5F5F5' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
          Notes
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            <Ionicons 
              name={viewMode === 'grid' ? 'list' : 'grid'} 
              size={24} 
              color={isDark ? '#FFFFFF' : '#000000'} 
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddModal(true)}
          >
            <Ionicons name="add" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' }]}>
        <Ionicons name="search" size={20} color={isDark ? '#CCCCCC' : '#666666'} />
        <TextInput
          style={[styles.searchInput, { color: isDark ? '#FFFFFF' : '#000000' }]}
          placeholder="Search notes..."
          placeholderTextColor={isDark ? '#666666' : '#999999'}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Folders */}
      {folders.length > 0 && (
        <View style={styles.foldersSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Folders
            </Text>
            <TouchableOpacity onPress={() => setShowFolderModal(true)}>
              <Ionicons name="add-circle-outline" size={20} color="#6200EE" />
            </TouchableOpacity>
          </View>
          <FlatList
            data={folders}
            renderItem={renderFolderItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.foldersList}
          />
        </View>
      )}

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        <TouchableOpacity
          style={[
            styles.filterTab,
            !showArchived && styles.activeFilterTab,
            { backgroundColor: !showArchived ? '#6200EE' : 'transparent' },
          ]}
          onPress={() => setShowArchived(false)}
        >
          <Text
            style={[
              styles.filterTabText,
              { color: !showArchived ? '#FFFFFF' : (isDark ? '#FFFFFF' : '#000000') },
            ]}
          >
            Notes ({filteredNotes.filter(n => !n.isArchived).length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterTab,
            showArchived && styles.activeFilterTab,
            { backgroundColor: showArchived ? '#6200EE' : 'transparent' },
          ]}
          onPress={() => setShowArchived(true)}
        >
          <Text
            style={[
              styles.filterTabText,
              { color: showArchived ? '#FFFFFF' : (isDark ? '#FFFFFF' : '#000000') },
            ]}
          >
            Archived ({notes.filter(n => n.isArchived).length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Notes List */}
      <FlatList
        data={filteredNotes}
        renderItem={renderNoteItem}
        keyExtractor={(item) => item.id}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        style={styles.notesList}
        contentContainerStyle={styles.notesContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="document-text-outline" size={64} color={isDark ? '#666666' : '#CCCCCC'} />
            <Text style={[styles.emptyText, { color: isDark ? '#666666' : '#999999' }]}>
              {searchQuery
                ? 'No notes match your search'
                : showArchived
                ? 'No archived notes'
                : 'No notes yet. Create your first note!'}
            </Text>
          </View>
        }
      />

      {/* Add Note Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#121212' : '#FFFFFF' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Add Note
            </Text>
            <TouchableOpacity onPress={handleAddNote}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Title
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter note title"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newNote.title}
                onChangeText={(text) => setNewNote({ ...newNote, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Content
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  styles.contentInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Start writing your note..."
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newNote.content}
                onChangeText={(text) => setNewNote({ ...newNote, content: text })}
                multiline
                numberOfLines={10}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Color
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.colorSelector}>
                  {NOTE_COLORS.map((color) => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        newNote.color === color && styles.selectedColorOption,
                        isDark && color === '#FFFFFF' && { borderColor: '#333333', borderWidth: 1 },
                      ]}
                      onPress={() => setNewNote({ ...newNote, color })}
                    >
                      {newNote.color === color && (
                        <Ionicons name="checkmark" size={16} color={color === '#FFFFFF' ? '#000000' : '#FFFFFF'} />
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            {folders.length > 0 && (
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                  Folder
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.folderSelector}>
                    <TouchableOpacity
                      style={[
                        styles.folderOption,
                        !newNote.folderId && styles.selectedFolderOption,
                        { 
                          backgroundColor: !newNote.folderId 
                            ? '#6200EE' 
                            : (isDark ? '#1E1E1E' : '#F5F5F5')
                        }
                      ]}
                      onPress={() => setNewNote({ ...newNote, folderId: undefined })}
                    >
                      <Text
                        style={[
                          styles.folderOptionText,
                          { 
                            color: !newNote.folderId 
                              ? '#FFFFFF' 
                              : (isDark ? '#FFFFFF' : '#000000')
                          }
                        ]}
                      >
                        No Folder
                      </Text>
                    </TouchableOpacity>
                    
                    {folders.map((folder) => (
                      <TouchableOpacity
                        key={folder.id}
                        style={[
                          styles.folderOption,
                          newNote.folderId === folder.id && styles.selectedFolderOption,
                          { 
                            backgroundColor: newNote.folderId === folder.id 
                              ? '#6200EE' 
                              : (isDark ? '#1E1E1E' : '#F5F5F5')
                          }
                        ]}
                        onPress={() => setNewNote({ 
                          ...newNote, 
                          folderId: newNote.folderId === folder.id ? undefined : folder.id 
                        })}
                      >
                        <Text
                          style={[
                            styles.folderOptionText,
                            { 
                              color: newNote.folderId === folder.id 
                                ? '#FFFFFF' 
                                : (isDark ? '#FFFFFF' : '#000000')
                            }
                          ]}
                        >
                          {folder.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Add Folder Modal */}
      <Modal
        visible={showFolderModal}
        animationType="slide"
        presentationStyle="formSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#121212' : '#FFFFFF' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowFolderModal(false)}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Add Folder
            </Text>
            <TouchableOpacity onPress={handleAddFolder}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Folder Name
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter folder name"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newFolderName}
                onChangeText={setNewFolderName}
                autoFocus
              />
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    padding: 8,
  },
  addButton: {
    backgroundColor: '#6200EE',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  foldersSection: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  foldersList: {
    paddingHorizontal: 20,
  },
  folderItem: {
    alignItems: 'center',
    padding: 12,
    marginRight: 12,
    borderRadius: 12,
    minWidth: 80,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  folderName: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
    textAlign: 'center',
  },
  folderCount: {
    fontSize: 10,
    marginTop: 2,
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
    gap: 8,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
  },
  activeFilterTab: {
    backgroundColor: '#6200EE',
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  notesList: {
    flex: 1,
  },
  notesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  noteItemGrid: {
    flex: 1,
    margin: 6,
    padding: 12,
    borderRadius: 12,
    minHeight: 120,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  noteItemList: {
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  noteActions: {
    flexDirection: 'row',
    gap: 8,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  noteContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  noteMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto',
  },
  noteDate: {
    fontSize: 12,
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tag: {
    backgroundColor: '#6200EE20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 10,
    color: '#6200EE',
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  contentInput: {
    height: 200,
    textAlignVertical: 'top',
  },
  colorSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: '#6200EE',
  },
  folderSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  folderOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  selectedFolderOption: {
    backgroundColor: '#6200EE',
  },
  folderOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
