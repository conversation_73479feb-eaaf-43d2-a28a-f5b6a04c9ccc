import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import {
    StyleSheet,
    TouchableOpacity,
    TouchableOpacityProps,
    View,
    ViewStyle
} from 'react-native';

// Helper function for cross-platform shadows
const getShadowStyle = (elevation = 2) => ({
  elevation: elevation,
  // Note: Shadow properties removed to avoid React Native Web warnings
});

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: number;
  margin?: number;
  elevation?: number;
  borderRadius?: number;
  backgroundColor?: string;
  onPress?: () => void;
  touchableProps?: Omit<TouchableOpacityProps, 'style' | 'onPress'>;
}

export function Card({
  children,
  style,
  padding = 16,
  margin = 0,
  elevation = 2,
  borderRadius = 12,
  backgroundColor,
  onPress,
  touchableProps,
}: CardProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const cardStyle: ViewStyle = {
    backgroundColor: backgroundColor || (isDark ? '#1E1E1E' : '#FFFFFF'),
    borderRadius,
    padding,
    margin,
    ...getShadowStyle(elevation),
    ...style,
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyle}
        onPress={onPress}
        activeOpacity={0.7}
        {...touchableProps}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return <View style={cardStyle}>{children}</View>;
}

// Specialized card components
interface ListCardProps extends Omit<CardProps, 'padding'> {
  children: React.ReactNode;
}

export function ListCard({ children, style, ...props }: ListCardProps) {
  return (
    <Card
      {...props}
      padding={0}
      style={[{ marginBottom: 8 }, style]}
    >
      {children}
    </Card>
  );
}

interface GridCardProps extends CardProps {
  aspectRatio?: number;
}

export function GridCard({ 
  children, 
  style, 
  aspectRatio = 1,
  ...props 
}: GridCardProps) {
  return (
    <Card
      {...props}
      style={[
        {
          flex: 1,
          aspectRatio,
          margin: 6,
        },
        style,
      ]}
    >
      {children}
    </Card>
  );
}

interface HeaderCardProps extends CardProps {
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

export function HeaderCard({ 
  children, 
  header, 
  footer, 
  style, 
  ...props 
}: HeaderCardProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Card {...props} padding={0} style={style}>
      {header && (
        <View style={[
          styles.headerSection,
          { borderBottomColor: isDark ? '#333333' : '#E0E0E0' }
        ]}>
          {header}
        </View>
      )}
      <View style={styles.contentSection}>
        {children}
      </View>
      {footer && (
        <View style={[
          styles.footerSection,
          { borderTopColor: isDark ? '#333333' : '#E0E0E0' }
        ]}>
          {footer}
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  headerSection: {
    padding: 16,
    borderBottomWidth: 1,
  },
  contentSection: {
    padding: 16,
  },
  footerSection: {
    padding: 16,
    borderTopWidth: 1,
  },
});
