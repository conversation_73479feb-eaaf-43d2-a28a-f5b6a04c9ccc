import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  ScrollView,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useReminderStore } from '@/store';
import { Reminder, ReminderType } from '@/types';
import { formatDate, formatTime, getRelativeDate } from '@/utils';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function RemindersScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const {
    reminders,
    addReminder,
    updateReminder,
    deleteReminder,
    snoozeReminder,
    dismissReminder,
    getActiveReminders,
  } = useReminderStore();

  const [showAddModal, setShowAddModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [newReminder, setNewReminder] = useState({
    title: '',
    description: '',
    type: 'time' as ReminderType,
    triggerTime: new Date(),
    isRecurring: false,
  });

  // Filter reminders
  const filteredReminders = useMemo(() => {
    let filtered = reminders.filter((reminder) => {
      const matchesSearch = reminder.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           reminder.description?.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesSearch;
    });

    // Sort by trigger time
    return filtered.sort((a, b) => {
      // Active reminders first
      if (a.notificationStatus === 'pending' && b.notificationStatus !== 'pending') return -1;
      if (b.notificationStatus === 'pending' && a.notificationStatus !== 'pending') return 1;
      
      // Then by trigger time
      if (a.triggerTime && b.triggerTime) {
        return new Date(a.triggerTime).getTime() - new Date(b.triggerTime).getTime();
      }
      
      return 0;
    });
  }, [reminders, searchQuery]);

  const handleAddReminder = () => {
    if (!newReminder.title.trim()) {
      Alert.alert('Error', 'Please enter a title for the reminder');
      return;
    }

    if (newReminder.type === 'time' && !newReminder.triggerTime) {
      Alert.alert('Error', 'Please set a time for the reminder');
      return;
    }

    addReminder({
      title: newReminder.title.trim(),
      description: newReminder.description.trim() || undefined,
      type: newReminder.type,
      triggerTime: newReminder.type === 'time' ? newReminder.triggerTime : undefined,
      notificationStatus: 'pending',
      isRecurring: newReminder.isRecurring,
      tags: [],
    });

    // Reset form
    setNewReminder({
      title: '',
      description: '',
      type: 'time',
      triggerTime: new Date(),
      isRecurring: false,
    });
    setShowAddModal(false);
  };

  const handleDeleteReminder = (id: string) => {
    Alert.alert(
      'Delete Reminder',
      'Are you sure you want to delete this reminder?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteReminder(id) },
      ]
    );
  };

  const handleSnoozeReminder = (id: string) => {
    Alert.alert(
      'Snooze Reminder',
      'How long would you like to snooze this reminder?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: '5 minutes', onPress: () => {
          const snoozeUntil = new Date();
          snoozeUntil.setMinutes(snoozeUntil.getMinutes() + 5);
          snoozeReminder(id, snoozeUntil);
        }},
        { text: '15 minutes', onPress: () => {
          const snoozeUntil = new Date();
          snoozeUntil.setMinutes(snoozeUntil.getMinutes() + 15);
          snoozeReminder(id, snoozeUntil);
        }},
        { text: '1 hour', onPress: () => {
          const snoozeUntil = new Date();
          snoozeUntil.setHours(snoozeUntil.getHours() + 1);
          snoozeReminder(id, snoozeUntil);
        }},
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#4CAF50';
      case 'sent': return '#FF9800';
      case 'dismissed': return '#9E9E9E';
      case 'snoozed': return '#2196F3';
      default: return '#9E9E9E';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'sent': return 'notifications';
      case 'dismissed': return 'checkmark-circle';
      case 'snoozed': return 'pause-circle';
      default: return 'help-circle';
    }
  };

  const renderReminderItem = ({ item }: { item: Reminder }) => {
    const isPending = item.notificationStatus === 'pending';
    const isSnoozed = item.notificationStatus === 'snoozed';
    
    return (
      <TouchableOpacity
        style={[
          styles.reminderItem,
          { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
        ]}
      >
        <View style={styles.reminderHeader}>
          <View style={styles.reminderContent}>
            <View style={styles.reminderTitleRow}>
              <Text
                style={[
                  styles.reminderTitle,
                  { color: isDark ? '#FFFFFF' : '#000000' },
                ]}
              >
                {item.title}
              </Text>
              
              <View style={styles.statusContainer}>
                <Ionicons 
                  name={getStatusIcon(item.notificationStatus) as any} 
                  size={16} 
                  color={getStatusColor(item.notificationStatus)} 
                />
                <Text style={[styles.statusText, { color: getStatusColor(item.notificationStatus) }]}>
                  {item.notificationStatus.charAt(0).toUpperCase() + item.notificationStatus.slice(1)}
                </Text>
              </View>
            </View>
            
            {item.description && (
              <Text
                style={[
                  styles.reminderDescription,
                  { color: isDark ? '#CCCCCC' : '#666666' },
                ]}
              >
                {item.description}
              </Text>
            )}
            
            <View style={styles.reminderMeta}>
              <View style={styles.typeTag}>
                <Ionicons 
                  name={item.type === 'time' ? 'time' : 'location'} 
                  size={12} 
                  color={isDark ? '#FFFFFF' : '#000000'} 
                />
                <Text style={[styles.typeText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                  {item.type === 'time' ? 'Time-based' : 'Location-based'}
                </Text>
              </View>
              
              {item.triggerTime && (
                <Text
                  style={[
                    styles.triggerTime,
                    { color: isDark ? '#CCCCCC' : '#666666' },
                  ]}
                >
                  {getRelativeDate(item.triggerTime)} at {formatTime(item.triggerTime)}
                </Text>
              )}
            </View>
            
            {isSnoozed && item.snoozeUntil && (
              <Text style={[styles.snoozeText, { color: '#2196F3' }]}>
                Snoozed until {formatTime(item.snoozeUntil)}
              </Text>
            )}
          </View>
          
          <View style={styles.actionButtons}>
            {isPending && (
              <>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: '#2196F3' }]}
                  onPress={() => handleSnoozeReminder(item.id)}
                >
                  <Ionicons name="pause" size={16} color="#FFFFFF" />
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
                  onPress={() => dismissReminder(item.id)}
                >
                  <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                </TouchableOpacity>
              </>
            )}
            
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#F44336' }]}
              onPress={() => handleDeleteReminder(item.id)}
            >
              <Ionicons name="trash" size={16} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#121212' : '#F5F5F5' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
          Reminders
        </Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' }]}>
        <Ionicons name="search" size={20} color={isDark ? '#CCCCCC' : '#666666'} />
        <TextInput
          style={[styles.searchInput, { color: isDark ? '#FFFFFF' : '#000000' }]}
          placeholder="Search reminders..."
          placeholderTextColor={isDark ? '#666666' : '#999999'}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Active Reminders Count */}
      <View style={styles.statsContainer}>
        <Text style={[styles.statsText, { color: isDark ? '#CCCCCC' : '#666666' }]}>
          {getActiveReminders().length} active reminder{getActiveReminders().length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Reminders List */}
      <FlatList
        data={filteredReminders}
        renderItem={renderReminderItem}
        keyExtractor={(item) => item.id}
        style={styles.remindersList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="notifications-outline" size={64} color={isDark ? '#666666' : '#CCCCCC'} />
            <Text style={[styles.emptyText, { color: isDark ? '#666666' : '#999999' }]}>
              {searchQuery
                ? 'No reminders match your search'
                : 'No reminders yet. Add one to get started!'}
            </Text>
          </View>
        }
      />

      {/* Add Reminder Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#121212' : '#FFFFFF' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Add Reminder
            </Text>
            <TouchableOpacity onPress={handleAddReminder}>
              <Text style={[styles.modalButton, { color: '#6200EE' }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Title *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter reminder title"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newReminder.title}
                onChangeText={(text) => setNewReminder({ ...newReminder, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Description
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  styles.multilineInput,
                  { 
                    backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5',
                    color: isDark ? '#FFFFFF' : '#000000',
                  }
                ]}
                placeholder="Enter description (optional)"
                placeholderTextColor={isDark ? '#666666' : '#999999'}
                value={newReminder.description}
                onChangeText={(text) => setNewReminder({ ...newReminder, description: text })}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                Type
              </Text>
              <View style={styles.typeSelector}>
                <TouchableOpacity
                  style={[
                    styles.typeOption,
                    newReminder.type === 'time' && styles.selectedTypeOption,
                    { 
                      backgroundColor: newReminder.type === 'time' 
                        ? '#6200EE' 
                        : (isDark ? '#1E1E1E' : '#F5F5F5')
                    }
                  ]}
                  onPress={() => setNewReminder({ ...newReminder, type: 'time' })}
                >
                  <Ionicons 
                    name="time" 
                    size={20} 
                    color={newReminder.type === 'time' ? '#FFFFFF' : (isDark ? '#FFFFFF' : '#000000')} 
                  />
                  <Text
                    style={[
                      styles.typeOptionText,
                      { 
                        color: newReminder.type === 'time' 
                          ? '#FFFFFF' 
                          : (isDark ? '#FFFFFF' : '#000000')
                      }
                    ]}
                  >
                    Time-based
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeOption,
                    newReminder.type === 'location' && styles.selectedTypeOption,
                    { 
                      backgroundColor: newReminder.type === 'location' 
                        ? '#6200EE' 
                        : (isDark ? '#1E1E1E' : '#F5F5F5')
                    }
                  ]}
                  onPress={() => setNewReminder({ ...newReminder, type: 'location' })}
                >
                  <Ionicons 
                    name="location" 
                    size={20} 
                    color={newReminder.type === 'location' ? '#FFFFFF' : (isDark ? '#FFFFFF' : '#000000')} 
                  />
                  <Text
                    style={[
                      styles.typeOptionText,
                      { 
                        color: newReminder.type === 'location' 
                          ? '#FFFFFF' 
                          : (isDark ? '#FFFFFF' : '#000000')
                      }
                    ]}
                  >
                    Location-based
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {newReminder.type === 'time' && (
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                  Date & Time
                </Text>
                <TouchableOpacity
                  style={[
                    styles.dateTimeButton,
                    { backgroundColor: isDark ? '#1E1E1E' : '#F5F5F5' }
                  ]}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Ionicons name="calendar" size={20} color={isDark ? '#FFFFFF' : '#000000'} />
                  <Text style={[styles.dateTimeText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                    {formatDate(newReminder.triggerTime)} at {formatTime(newReminder.triggerTime)}
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.inputGroup}>
              <View style={styles.switchRow}>
                <Text style={[styles.inputLabel, { color: isDark ? '#FFFFFF' : '#000000' }]}>
                  Recurring
                </Text>
                <Switch
                  value={newReminder.isRecurring}
                  onValueChange={(value) => setNewReminder({ ...newReminder, isRecurring: value })}
                  trackColor={{ false: '#767577', true: '#6200EE' }}
                  thumbColor={newReminder.isRecurring ? '#FFFFFF' : '#f4f3f4'}
                />
              </View>
            </View>
          </ScrollView>

          {/* Date/Time Pickers */}
          {showDatePicker && (
            <DateTimePicker
              value={newReminder.triggerTime}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowDatePicker(false);
                if (selectedDate) {
                  setNewReminder({ ...newReminder, triggerTime: selectedDate });
                  setShowTimePicker(true);
                }
              }}
            />
          )}

          {showTimePicker && (
            <DateTimePicker
              value={newReminder.triggerTime}
              mode="time"
              display="default"
              onChange={(event, selectedTime) => {
                setShowTimePicker(false);
                if (selectedTime) {
                  setNewReminder({ ...newReminder, triggerTime: selectedTime });
                }
              }}
            />
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    backgroundColor: '#6200EE',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  statsText: {
    fontSize: 14,
    fontWeight: '500',
  },
  remindersList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  reminderItem: {
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  reminderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  reminderContent: {
    flex: 1,
    marginRight: 12,
  },
  reminderTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  reminderTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  reminderDescription: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  reminderMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  typeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  triggerTime: {
    fontSize: 12,
    fontWeight: '500',
  },
  snoozeText: {
    fontSize: 12,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  typeOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
  },
  selectedTypeOption: {
    backgroundColor: '#6200EE',
  },
  typeOptionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  dateTimeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 12,
  },
  dateTimeText: {
    fontSize: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
