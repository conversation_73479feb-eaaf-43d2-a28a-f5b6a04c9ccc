// Core data types for the productivity app

export type Priority = 'low' | 'medium' | 'high' | 'urgent';
export type TaskStatus = 'pending' | 'completed' | 'cancelled';
export type RecurrenceType = 'none' | 'daily' | 'weekly' | 'monthly' | 'yearly';
export type ReminderType = 'time' | 'location';
export type NotificationStatus = 'pending' | 'sent' | 'dismissed' | 'snoozed';

// Todo Item Interface
export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  category?: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  recurrence: {
    type: RecurrenceType;
    interval?: number; // For custom intervals (e.g., every 2 weeks)
    endDate?: Date;
  };
  tags: string[];
  subtasks: SubTask[];
}

export interface SubTask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date;
}

// Reminder Interface
export interface Reminder {
  id: string;
  title: string;
  description?: string;
  type: ReminderType;
  triggerTime?: Date;
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in meters
    address?: string;
  };
  notificationStatus: NotificationStatus;
  createdAt: Date;
  updatedAt: Date;
  isRecurring: boolean;
  recurrence?: {
    type: RecurrenceType;
    interval?: number;
    endDate?: Date;
  };
  snoozeUntil?: Date;
  tags: string[];
}

// Note Interface
export interface Note {
  id: string;
  title: string;
  content: string; // Rich text content (HTML or markdown)
  folderId?: string;
  color?: string;
  tags: string[];
  attachments: Attachment[];
  checklist: ChecklistItem[];
  createdAt: Date;
  updatedAt: Date;
  isPinned: boolean;
  isArchived: boolean;
}

export interface Attachment {
  id: string;
  type: 'image' | 'file';
  uri: string;
  name: string;
  size?: number;
  mimeType?: string;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  createdAt: Date;
}

export interface Folder {
  id: string;
  name: string;
  color?: string;
  createdAt: Date;
  parentId?: string; // For nested folders
}

// Calendar Event Interface
export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  isAllDay: boolean;
  location?: string;
  attendees: string[];
  reminders: EventReminder[];
  recurrence?: {
    type: RecurrenceType;
    interval?: number;
    endDate?: Date;
    exceptions?: Date[]; // Dates to skip
  };
  color?: string;
  category?: string;
  createdAt: Date;
  updatedAt: Date;
  externalCalendarId?: string; // For synced events
  externalEventId?: string;
}

export interface EventReminder {
  id: string;
  minutesBefore: number;
  type: 'notification' | 'email' | 'popup';
}

// App Settings Interface
export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    enabled: boolean;
    soundEnabled: boolean;
    vibrationEnabled: boolean;
    quietHours: {
      enabled: boolean;
      startTime: string; // HH:mm format
      endTime: string;
    };
  };
  calendar: {
    defaultView: 'day' | 'week' | 'month';
    weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 = Sunday
    workingHours: {
      start: string; // HH:mm format
      end: string;
    };
  };
  sync: {
    googleCalendar: {
      enabled: boolean;
      lastSync?: Date;
    };
    outlookCalendar: {
      enabled: boolean;
      lastSync?: Date;
    };
  };
  accessibility: {
    fontSize: 'small' | 'medium' | 'large' | 'extra-large';
    highContrast: boolean;
    reduceMotion: boolean;
  };
}

// Navigation and UI Types
export type TabName = 'todos' | 'reminders' | 'notes' | 'calendar';

export interface TabConfig {
  name: TabName;
  title: string;
  icon: string;
  component: React.ComponentType;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Search and Filter Types
export interface SearchFilters {
  query?: string;
  tags?: string[];
  categories?: string[];
  priority?: Priority[];
  status?: TaskStatus[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface SortOptions {
  field: 'title' | 'createdAt' | 'updatedAt' | 'dueDate' | 'priority';
  direction: 'asc' | 'desc';
}

// Sync and Backup Types
export interface SyncStatus {
  lastSync: Date;
  isInProgress: boolean;
  hasErrors: boolean;
  errorMessage?: string;
}

export interface BackupData {
  todos: TodoItem[];
  reminders: Reminder[];
  notes: Note[];
  events: CalendarEvent[];
  folders: Folder[];
  settings: AppSettings;
  exportedAt: Date;
  version: string;
}
