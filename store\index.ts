import {
    AppSettings,
    CalendarEvent,
    Folder,
    Note,
    Priority,
    Reminder,
    TodoItem
} from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// Todo Store
interface TodoStore {
  todos: TodoItem[];
  categories: string[];
  addTodo: (todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateTodo: (id: string, updates: Partial<TodoItem>) => void;
  deleteTodo: (id: string) => void;
  toggleTodoStatus: (id: string) => void;
  addCategory: (category: string) => void;
  removeCategory: (category: string) => void;
  getTodosByCategory: (category?: string) => TodoItem[];
  getTodosByPriority: (priority: Priority) => TodoItem[];
  getOverdueTodos: () => TodoItem[];
}

export const useTodoStore = create<TodoStore>()(
  persist(
    (set, get) => ({
      todos: [],
      categories: ['Personal', 'Work', 'Shopping', 'Health'],
      
      addTodo: (todoData) => {
        const newTodo: TodoItem = {
          ...todoData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date(),
          updatedAt: new Date(),
          status: 'pending',
          tags: todoData.tags || [],
          subtasks: todoData.subtasks || [],
          recurrence: todoData.recurrence || { type: 'none' }
        };
        set((state) => ({ todos: [...state.todos, newTodo] }));
      },
      
      updateTodo: (id, updates) => {
        set((state) => ({
          todos: state.todos.map((todo) =>
            todo.id === id 
              ? { ...todo, ...updates, updatedAt: new Date() }
              : todo
          )
        }));
      },
      
      deleteTodo: (id) => {
        set((state) => ({
          todos: state.todos.filter((todo) => todo.id !== id)
        }));
      },
      
      toggleTodoStatus: (id) => {
        set((state) => ({
          todos: state.todos.map((todo) =>
            todo.id === id
              ? {
                  ...todo,
                  status: todo.status === 'completed' ? 'pending' : 'completed',
                  completedAt: todo.status === 'pending' ? new Date() : undefined,
                  updatedAt: new Date()
                }
              : todo
          )
        }));
      },
      
      addCategory: (category) => {
        set((state) => ({
          categories: [...new Set([...state.categories, category])]
        }));
      },
      
      removeCategory: (category) => {
        set((state) => ({
          categories: state.categories.filter((cat) => cat !== category)
        }));
      },
      
      getTodosByCategory: (category) => {
        const { todos } = get();
        return category 
          ? todos.filter((todo) => todo.category === category)
          : todos.filter((todo) => !todo.category);
      },
      
      getTodosByPriority: (priority) => {
        const { todos } = get();
        return todos.filter((todo) => todo.priority === priority);
      },
      
      getOverdueTodos: () => {
        const { todos } = get();
        const now = new Date();
        return todos.filter((todo) => 
          todo.dueDate && 
          new Date(todo.dueDate) < now && 
          todo.status !== 'completed'
        );
      }
    }),
    {
      name: 'todo-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// Reminder Store
interface ReminderStore {
  reminders: Reminder[];
  addReminder: (reminder: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateReminder: (id: string, updates: Partial<Reminder>) => void;
  deleteReminder: (id: string) => void;
  snoozeReminder: (id: string, snoozeUntil: Date) => void;
  dismissReminder: (id: string) => void;
  getActiveReminders: () => Reminder[];
  getPendingReminders: () => Reminder[];
}

export const useReminderStore = create<ReminderStore>()(
  persist(
    (set, get) => ({
      reminders: [],
      
      addReminder: (reminderData) => {
        const newReminder: Reminder = {
          ...reminderData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date(),
          updatedAt: new Date(),
          notificationStatus: 'pending',
          tags: reminderData.tags || []
        };
        set((state) => ({ reminders: [...state.reminders, newReminder] }));
      },
      
      updateReminder: (id, updates) => {
        set((state) => ({
          reminders: state.reminders.map((reminder) =>
            reminder.id === id 
              ? { ...reminder, ...updates, updatedAt: new Date() }
              : reminder
          )
        }));
      },
      
      deleteReminder: (id) => {
        set((state) => ({
          reminders: state.reminders.filter((reminder) => reminder.id !== id)
        }));
      },
      
      snoozeReminder: (id, snoozeUntil) => {
        set((state) => ({
          reminders: state.reminders.map((reminder) =>
            reminder.id === id
              ? { 
                  ...reminder, 
                  notificationStatus: 'snoozed',
                  snoozeUntil,
                  updatedAt: new Date()
                }
              : reminder
          )
        }));
      },
      
      dismissReminder: (id) => {
        set((state) => ({
          reminders: state.reminders.map((reminder) =>
            reminder.id === id
              ? { 
                  ...reminder, 
                  notificationStatus: 'dismissed',
                  updatedAt: new Date()
                }
              : reminder
          )
        }));
      },
      
      getActiveReminders: () => {
        const { reminders } = get();
        const now = new Date();
        return reminders.filter((reminder) => 
          reminder.notificationStatus === 'pending' &&
          (!reminder.snoozeUntil || new Date(reminder.snoozeUntil) <= now)
        );
      },
      
      getPendingReminders: () => {
        const { reminders } = get();
        return reminders.filter((reminder) => 
          reminder.notificationStatus === 'pending'
        );
      }
    }),
    {
      name: 'reminder-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// Notes Store
interface NoteStore {
  notes: Note[];
  folders: Folder[];
  addNote: (note: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateNote: (id: string, updates: Partial<Note>) => void;
  deleteNote: (id: string) => void;
  addFolder: (folder: Omit<Folder, 'id' | 'createdAt'>) => void;
  updateFolder: (id: string, updates: Partial<Folder>) => void;
  deleteFolder: (id: string) => void;
  getNotesByFolder: (folderId?: string) => Note[];
  searchNotes: (query: string) => Note[];
  togglePinNote: (id: string) => void;
  toggleArchiveNote: (id: string) => void;
}

export const useNoteStore = create<NoteStore>()(
  persist(
    (set, get) => ({
      notes: [],
      folders: [],
      
      addNote: (noteData) => {
        const newNote: Note = {
          ...noteData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date(),
          updatedAt: new Date(),
          tags: noteData.tags || [],
          attachments: noteData.attachments || [],
          checklist: noteData.checklist || [],
          isPinned: false,
          isArchived: false
        };
        set((state) => ({ notes: [...state.notes, newNote] }));
      },
      
      updateNote: (id, updates) => {
        set((state) => ({
          notes: state.notes.map((note) =>
            note.id === id 
              ? { ...note, ...updates, updatedAt: new Date() }
              : note
          )
        }));
      },
      
      deleteNote: (id) => {
        set((state) => ({
          notes: state.notes.filter((note) => note.id !== id)
        }));
      },
      
      addFolder: (folderData) => {
        const newFolder: Folder = {
          ...folderData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date()
        };
        set((state) => ({ folders: [...state.folders, newFolder] }));
      },
      
      updateFolder: (id, updates) => {
        set((state) => ({
          folders: state.folders.map((folder) =>
            folder.id === id ? { ...folder, ...updates } : folder
          )
        }));
      },
      
      deleteFolder: (id) => {
        set((state) => ({
          folders: state.folders.filter((folder) => folder.id !== id),
          notes: state.notes.map((note) =>
            note.folderId === id ? { ...note, folderId: undefined } : note
          )
        }));
      },
      
      getNotesByFolder: (folderId) => {
        const { notes } = get();
        return folderId 
          ? notes.filter((note) => note.folderId === folderId)
          : notes.filter((note) => !note.folderId);
      },
      
      searchNotes: (query) => {
        const { notes } = get();
        const lowercaseQuery = query.toLowerCase();
        return notes.filter((note) =>
          note.title.toLowerCase().includes(lowercaseQuery) ||
          note.content.toLowerCase().includes(lowercaseQuery) ||
          note.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery))
        );
      },
      
      togglePinNote: (id) => {
        set((state) => ({
          notes: state.notes.map((note) =>
            note.id === id 
              ? { ...note, isPinned: !note.isPinned, updatedAt: new Date() }
              : note
          )
        }));
      },
      
      toggleArchiveNote: (id) => {
        set((state) => ({
          notes: state.notes.map((note) =>
            note.id === id 
              ? { ...note, isArchived: !note.isArchived, updatedAt: new Date() }
              : note
          )
        }));
      }
    }),
    {
      name: 'note-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// Calendar Store
interface CalendarStore {
  events: CalendarEvent[];
  addEvent: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateEvent: (id: string, updates: Partial<CalendarEvent>) => void;
  deleteEvent: (id: string) => void;
  getEventsByDate: (date: Date) => CalendarEvent[];
  getEventsByDateRange: (startDate: Date, endDate: Date) => CalendarEvent[];
  getUpcomingEvents: (days?: number) => CalendarEvent[];
}

export const useCalendarStore = create<CalendarStore>()(
  persist(
    (set, get) => ({
      events: [],

      addEvent: (eventData) => {
        const newEvent: CalendarEvent = {
          ...eventData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date(),
          updatedAt: new Date(),
          attendees: eventData.attendees || [],
          reminders: eventData.reminders || []
        };
        set((state) => ({ events: [...state.events, newEvent] }));
      },

      updateEvent: (id, updates) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === id
              ? { ...event, ...updates, updatedAt: new Date() }
              : event
          )
        }));
      },

      deleteEvent: (id) => {
        set((state) => ({
          events: state.events.filter((event) => event.id !== id)
        }));
      },

      getEventsByDate: (date) => {
        const { events } = get();
        const targetDate = new Date(date);
        targetDate.setHours(0, 0, 0, 0);
        const nextDay = new Date(targetDate);
        nextDay.setDate(nextDay.getDate() + 1);

        return events.filter((event) => {
          const eventStart = new Date(event.startDate);
          const eventEnd = new Date(event.endDate);
          return (eventStart >= targetDate && eventStart < nextDay) ||
                 (eventEnd >= targetDate && eventEnd < nextDay) ||
                 (eventStart < targetDate && eventEnd >= nextDay);
        });
      },

      getEventsByDateRange: (startDate, endDate) => {
        const { events } = get();
        return events.filter((event) => {
          const eventStart = new Date(event.startDate);
          const eventEnd = new Date(event.endDate);
          return (eventStart >= startDate && eventStart <= endDate) ||
                 (eventEnd >= startDate && eventEnd <= endDate) ||
                 (eventStart < startDate && eventEnd > endDate);
        });
      },

      getUpcomingEvents: (days = 7) => {
        const { events } = get();
        const now = new Date();
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + days);

        return events
          .filter((event) => new Date(event.startDate) >= now && new Date(event.startDate) <= futureDate)
          .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
      }
    }),
    {
      name: 'calendar-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// Settings Store
interface SettingsStore {
  settings: AppSettings;
  updateSettings: (updates: Partial<AppSettings>) => void;
  resetSettings: () => void;
}

const defaultSettings: AppSettings = {
  theme: 'system',
  notifications: {
    enabled: true,
    soundEnabled: true,
    vibrationEnabled: true,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00'
    }
  },
  calendar: {
    defaultView: 'week',
    weekStartsOn: 1, // Monday
    workingHours: {
      start: '09:00',
      end: '17:00'
    }
  },
  sync: {
    googleCalendar: {
      enabled: false
    },
    outlookCalendar: {
      enabled: false
    }
  },
  accessibility: {
    fontSize: 'medium',
    highContrast: false,
    reduceMotion: false
  }
};

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set) => ({
      settings: defaultSettings,

      updateSettings: (updates) => {
        set((state) => ({
          settings: { ...state.settings, ...updates }
        }));
      },

      resetSettings: () => {
        set({ settings: defaultSettings });
      }
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
