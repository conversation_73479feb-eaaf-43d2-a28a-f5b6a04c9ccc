import React, { createContext, useContext, useEffect, useState } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import { useSettingsStore } from '@/store';

export interface ThemeColors {
  primary: string;
  primaryVariant: string;
  secondary: string;
  secondaryVariant: string;
  background: string;
  surface: string;
  error: string;
  warning: string;
  success: string;
  info: string;
  onPrimary: string;
  onSecondary: string;
  onBackground: string;
  onSurface: string;
  onError: string;
  text: {
    primary: string;
    secondary: string;
    disabled: string;
    hint: string;
  };
  border: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  shadow: string;
}

export interface Theme {
  colors: ThemeColors;
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  typography: {
    h1: { fontSize: number; fontWeight: string; lineHeight: number };
    h2: { fontSize: number; fontWeight: string; lineHeight: number };
    h3: { fontSize: number; fontWeight: string; lineHeight: number };
    h4: { fontSize: number; fontWeight: string; lineHeight: number };
    h5: { fontSize: number; fontWeight: string; lineHeight: number };
    h6: { fontSize: number; fontWeight: string; lineHeight: number };
    body1: { fontSize: number; fontWeight: string; lineHeight: number };
    body2: { fontSize: number; fontWeight: string; lineHeight: number };
    caption: { fontSize: number; fontWeight: string; lineHeight: number };
    button: { fontSize: number; fontWeight: string; lineHeight: number };
  };
  borderRadius: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    round: number;
  };
  elevation: {
    none: number;
    low: number;
    medium: number;
    high: number;
  };
}

const lightColors: ThemeColors = {
  primary: '#6200EE',
  primaryVariant: '#3700B3',
  secondary: '#03DAC6',
  secondaryVariant: '#018786',
  background: '#F5F5F5',
  surface: '#FFFFFF',
  error: '#F44336',
  warning: '#FF9800',
  success: '#4CAF50',
  info: '#2196F3',
  onPrimary: '#FFFFFF',
  onSecondary: '#000000',
  onBackground: '#000000',
  onSurface: '#000000',
  onError: '#FFFFFF',
  text: {
    primary: '#000000',
    secondary: '#666666',
    disabled: '#999999',
    hint: '#CCCCCC',
  },
  border: {
    primary: '#E0E0E0',
    secondary: '#F0F0F0',
    disabled: '#F5F5F5',
  },
  shadow: '#000000',
};

const darkColors: ThemeColors = {
  primary: '#BB86FC',
  primaryVariant: '#3700B3',
  secondary: '#03DAC6',
  secondaryVariant: '#03DAC6',
  background: '#121212',
  surface: '#1E1E1E',
  error: '#CF6679',
  warning: '#FFB74D',
  success: '#81C784',
  info: '#64B5F6',
  onPrimary: '#000000',
  onSecondary: '#000000',
  onBackground: '#FFFFFF',
  onSurface: '#FFFFFF',
  onError: '#000000',
  text: {
    primary: '#FFFFFF',
    secondary: '#CCCCCC',
    disabled: '#666666',
    hint: '#999999',
  },
  border: {
    primary: '#333333',
    secondary: '#2E2E2E',
    disabled: '#1E1E1E',
  },
  shadow: '#000000',
};

const baseTheme = {
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  typography: {
    h1: { fontSize: 32, fontWeight: 'bold', lineHeight: 40 },
    h2: { fontSize: 28, fontWeight: 'bold', lineHeight: 36 },
    h3: { fontSize: 24, fontWeight: '600', lineHeight: 32 },
    h4: { fontSize: 20, fontWeight: '600', lineHeight: 28 },
    h5: { fontSize: 18, fontWeight: '600', lineHeight: 24 },
    h6: { fontSize: 16, fontWeight: '600', lineHeight: 22 },
    body1: { fontSize: 16, fontWeight: 'normal', lineHeight: 24 },
    body2: { fontSize: 14, fontWeight: 'normal', lineHeight: 20 },
    caption: { fontSize: 12, fontWeight: 'normal', lineHeight: 16 },
    button: { fontSize: 16, fontWeight: '600', lineHeight: 20 },
  },
  borderRadius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    round: 9999,
  },
  elevation: {
    none: 0,
    low: 2,
    medium: 4,
    high: 8,
  },
};

const lightTheme: Theme = {
  ...baseTheme,
  colors: lightColors,
};

const darkTheme: Theme = {
  ...baseTheme,
  colors: darkColors,
};

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { settings, updateSettings } = useSettingsStore();
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );

  // Listen to system color scheme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription?.remove();
  }, []);

  // Determine current theme
  const isDark = settings.theme === 'dark' || 
    (settings.theme === 'system' && systemColorScheme === 'dark');
  
  const theme = isDark ? darkTheme : lightTheme;

  // Apply accessibility settings
  const accessibilityTheme = React.useMemo(() => {
    const adjustedTheme = { ...theme };
    
    // Adjust font sizes based on accessibility settings
    const fontSizeMultiplier = {
      small: 0.875,
      medium: 1,
      large: 1.125,
      'extra-large': 1.25,
    }[settings.accessibility.fontSize];

    Object.keys(adjustedTheme.typography).forEach(key => {
      const typographyKey = key as keyof typeof adjustedTheme.typography;
      adjustedTheme.typography[typographyKey] = {
        ...adjustedTheme.typography[typographyKey],
        fontSize: Math.round(adjustedTheme.typography[typographyKey].fontSize * fontSizeMultiplier),
      };
    });

    // Apply high contrast if enabled
    if (settings.accessibility.highContrast) {
      adjustedTheme.colors = {
        ...adjustedTheme.colors,
        text: {
          ...adjustedTheme.colors.text,
          primary: isDark ? '#FFFFFF' : '#000000',
          secondary: isDark ? '#E0E0E0' : '#333333',
        },
        border: {
          ...adjustedTheme.colors.border,
          primary: isDark ? '#666666' : '#999999',
        },
      };
    }

    return adjustedTheme;
  }, [theme, settings.accessibility, isDark]);

  const toggleTheme = () => {
    const newTheme = isDark ? 'light' : 'dark';
    updateSettings({ theme: newTheme });
  };

  const setTheme = (themeMode: 'light' | 'dark' | 'system') => {
    updateSettings({ theme: themeMode });
  };

  const contextValue: ThemeContextType = {
    theme: accessibilityTheme,
    isDark,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Utility hooks for common theme values
export function useThemeColors(): ThemeColors {
  const { theme } = useTheme();
  return theme.colors;
}

export function useThemeSpacing() {
  const { theme } = useTheme();
  return theme.spacing;
}

export function useThemeTypography() {
  const { theme } = useTheme();
  return theme.typography;
}

// Style helpers
export const createThemedStyles = <T extends Record<string, any>>(
  styleFactory: (theme: Theme) => T
) => {
  return (theme: Theme): T => styleFactory(theme);
};

// Common style utilities
export const getElevationStyle = (elevation: number, isDark: boolean = false) => ({
  elevation,
  shadowColor: isDark ? '#FFFFFF' : '#000000',
  shadowOffset: { width: 0, height: elevation / 2 },
  shadowOpacity: isDark ? 0.3 : 0.1,
  shadowRadius: elevation,
});

export const getSpacingStyle = (
  spacing: keyof Theme['spacing'],
  theme: Theme,
  direction?: 'horizontal' | 'vertical' | 'top' | 'bottom' | 'left' | 'right'
) => {
  const value = theme.spacing[spacing];
  
  switch (direction) {
    case 'horizontal':
      return { paddingHorizontal: value };
    case 'vertical':
      return { paddingVertical: value };
    case 'top':
      return { paddingTop: value };
    case 'bottom':
      return { paddingBottom: value };
    case 'left':
      return { paddingLeft: value };
    case 'right':
      return { paddingRight: value };
    default:
      return { padding: value };
  }
};
