import * as Calendar from 'expo-calendar';
import { CalendarEvent, SyncStatus } from '@/types';
import { StorageService } from './storage';

// External calendar sync service
export class SyncService {
  private static isInitialized = false;

  // Initialize calendar permissions
  static async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) return true;

      const { status } = await Calendar.requestCalendarPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Calendar permissions not granted');
        return false;
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize sync service:', error);
      return false;
    }
  }

  // Get available calendars
  static async getAvailableCalendars(): Promise<Calendar.Calendar[]> {
    try {
      const initialized = await this.initialize();
      if (!initialized) return [];

      const calendars = await Calendar.getCalendarsAsync(Calendar.EntityTypes.EVENT);
      return calendars.filter(calendar => calendar.allowsModifications);
    } catch (error) {
      console.error('Failed to get available calendars:', error);
      return [];
    }
  }

  // Sync events from external calendar
  static async syncFromExternalCalendar(
    calendarId: string,
    startDate: Date,
    endDate: Date
  ): Promise<CalendarEvent[]> {
    try {
      const initialized = await this.initialize();
      if (!initialized) return [];

      const externalEvents = await Calendar.getEventsAsync(
        [calendarId],
        startDate,
        endDate
      );

      const syncedEvents: CalendarEvent[] = externalEvents.map(event => ({
        id: `external_${event.id}`,
        title: event.title,
        description: event.notes || undefined,
        startDate: new Date(event.startDate),
        endDate: new Date(event.endDate),
        isAllDay: event.allDay,
        location: event.location || undefined,
        attendees: [],
        reminders: [],
        color: '#6200EE',
        createdAt: new Date(),
        updatedAt: new Date(),
        externalCalendarId: calendarId,
        externalEventId: event.id,
      }));

      return syncedEvents;
    } catch (error) {
      console.error('Failed to sync from external calendar:', error);
      return [];
    }
  }

  // Export event to external calendar
  static async exportToExternalCalendar(
    event: CalendarEvent,
    calendarId: string
  ): Promise<string | null> {
    try {
      const initialized = await this.initialize();
      if (!initialized) return null;

      const externalEventId = await Calendar.createEventAsync(calendarId, {
        title: event.title,
        notes: event.description,
        startDate: event.startDate,
        endDate: event.endDate,
        allDay: event.isAllDay,
        location: event.location,
        alarms: event.reminders.map(reminder => ({
          relativeOffset: -reminder.minutesBefore,
        })),
      });

      return externalEventId;
    } catch (error) {
      console.error('Failed to export to external calendar:', error);
      return null;
    }
  }

  // Update external calendar event
  static async updateExternalCalendarEvent(
    event: CalendarEvent
  ): Promise<boolean> {
    try {
      if (!event.externalEventId) return false;

      const initialized = await this.initialize();
      if (!initialized) return false;

      await Calendar.updateEventAsync(event.externalEventId, {
        title: event.title,
        notes: event.description,
        startDate: event.startDate,
        endDate: event.endDate,
        allDay: event.isAllDay,
        location: event.location,
        alarms: event.reminders.map(reminder => ({
          relativeOffset: -reminder.minutesBefore,
        })),
      });

      return true;
    } catch (error) {
      console.error('Failed to update external calendar event:', error);
      return false;
    }
  }

  // Delete external calendar event
  static async deleteExternalCalendarEvent(
    externalEventId: string
  ): Promise<boolean> {
    try {
      const initialized = await this.initialize();
      if (!initialized) return false;

      await Calendar.deleteEventAsync(externalEventId);
      return true;
    } catch (error) {
      console.error('Failed to delete external calendar event:', error);
      return false;
    }
  }

  // Two-way sync with external calendar
  static async performTwoWaySync(
    localEvents: CalendarEvent[],
    calendarId: string,
    syncRange: { start: Date; end: Date }
  ): Promise<SyncResult> {
    try {
      const result: SyncResult = {
        success: false,
        eventsAdded: 0,
        eventsUpdated: 0,
        eventsDeleted: 0,
        conflicts: [],
        errors: [],
      };

      const initialized = await this.initialize();
      if (!initialized) {
        result.errors.push('Sync service not initialized');
        return result;
      }

      // Get external events
      const externalEvents = await this.syncFromExternalCalendar(
        calendarId,
        syncRange.start,
        syncRange.end
      );

      // Find events to sync from external to local
      const externalEventIds = new Set(externalEvents.map(e => e.externalEventId));
      const localExternalEvents = localEvents.filter(e => e.externalCalendarId === calendarId);
      const localExternalEventIds = new Set(localExternalEvents.map(e => e.externalEventId));

      // Events to add to local (exist in external but not in local)
      const eventsToAddLocally = externalEvents.filter(
        e => !localExternalEventIds.has(e.externalEventId)
      );

      // Events to update locally (exist in both but may be different)
      const eventsToUpdateLocally = externalEvents.filter(e => {
        const localEvent = localExternalEvents.find(le => le.externalEventId === e.externalEventId);
        return localEvent && this.eventsAreDifferent(localEvent, e);
      });

      // Events to delete locally (exist in local but not in external)
      const eventsToDeleteLocally = localExternalEvents.filter(
        e => !externalEventIds.has(e.externalEventId)
      );

      // Find events to sync from local to external
      const localOnlyEvents = localEvents.filter(e => !e.externalCalendarId);

      // Export local-only events to external calendar
      for (const event of localOnlyEvents) {
        try {
          const externalEventId = await this.exportToExternalCalendar(event, calendarId);
          if (externalEventId) {
            // Update local event with external ID
            event.externalCalendarId = calendarId;
            event.externalEventId = externalEventId;
            result.eventsAdded++;
          }
        } catch (error) {
          result.errors.push(`Failed to export event "${event.title}": ${error}`);
        }
      }

      // Update external events that were modified locally
      const localModifiedEvents = localExternalEvents.filter(e => {
        const externalEvent = externalEvents.find(ee => ee.externalEventId === e.externalEventId);
        return externalEvent && this.eventsAreDifferent(e, externalEvent);
      });

      for (const event of localModifiedEvents) {
        try {
          const success = await this.updateExternalCalendarEvent(event);
          if (success) {
            result.eventsUpdated++;
          }
        } catch (error) {
          result.errors.push(`Failed to update event "${event.title}": ${error}`);
        }
      }

      result.success = result.errors.length === 0;
      await StorageService.setLastSyncTime(new Date());

      return result;
    } catch (error) {
      console.error('Two-way sync failed:', error);
      return {
        success: false,
        eventsAdded: 0,
        eventsUpdated: 0,
        eventsDeleted: 0,
        conflicts: [],
        errors: [error.toString()],
      };
    }
  }

  // Check if two events are different
  private static eventsAreDifferent(event1: CalendarEvent, event2: CalendarEvent): boolean {
    return (
      event1.title !== event2.title ||
      event1.description !== event2.description ||
      event1.startDate.getTime() !== event2.startDate.getTime() ||
      event1.endDate.getTime() !== event2.endDate.getTime() ||
      event1.isAllDay !== event2.isAllDay ||
      event1.location !== event2.location
    );
  }

  // Get sync status
  static async getSyncStatus(): Promise<SyncStatus> {
    try {
      const lastSync = await StorageService.getLastSyncTime();
      
      return {
        lastSync: lastSync || new Date(0),
        isInProgress: false, // This would be managed by the calling code
        hasErrors: false,
      };
    } catch (error) {
      console.error('Failed to get sync status:', error);
      return {
        lastSync: new Date(0),
        isInProgress: false,
        hasErrors: true,
        errorMessage: error.toString(),
      };
    }
  }

  // Create a new calendar for the app
  static async createAppCalendar(name: string = 'Productivity App'): Promise<string | null> {
    try {
      const initialized = await this.initialize();
      if (!initialized) return null;

      // Get default calendar source
      const defaultCalendarSource = await Calendar.getDefaultCalendarAsync();
      
      const calendarId = await Calendar.createCalendarAsync({
        title: name,
        color: '#6200EE',
        entityType: Calendar.EntityTypes.EVENT,
        sourceId: defaultCalendarSource.source.id,
        source: defaultCalendarSource.source,
        name: name,
        ownerAccount: 'personal',
        accessLevel: Calendar.CalendarAccessLevel.OWNER,
      });

      return calendarId;
    } catch (error) {
      console.error('Failed to create app calendar:', error);
      return null;
    }
  }

  // Batch sync operations
  static async batchSync(
    operations: SyncOperation[]
  ): Promise<BatchSyncResult> {
    const result: BatchSyncResult = {
      success: true,
      results: [],
      totalOperations: operations.length,
      successfulOperations: 0,
      failedOperations: 0,
    };

    for (const operation of operations) {
      try {
        let operationResult: any = null;

        switch (operation.type) {
          case 'export':
            operationResult = await this.exportToExternalCalendar(
              operation.event,
              operation.calendarId!
            );
            break;
          case 'update':
            operationResult = await this.updateExternalCalendarEvent(operation.event);
            break;
          case 'delete':
            operationResult = await this.deleteExternalCalendarEvent(
              operation.event.externalEventId!
            );
            break;
          case 'import':
            operationResult = await this.syncFromExternalCalendar(
              operation.calendarId!,
              operation.startDate!,
              operation.endDate!
            );
            break;
        }

        result.results.push({
          operation,
          success: operationResult !== null && operationResult !== false,
          result: operationResult,
        });

        if (operationResult !== null && operationResult !== false) {
          result.successfulOperations++;
        } else {
          result.failedOperations++;
          result.success = false;
        }
      } catch (error) {
        result.results.push({
          operation,
          success: false,
          error: error.toString(),
        });
        result.failedOperations++;
        result.success = false;
      }
    }

    return result;
  }
}

// Types for sync operations
export interface SyncResult {
  success: boolean;
  eventsAdded: number;
  eventsUpdated: number;
  eventsDeleted: number;
  conflicts: SyncConflict[];
  errors: string[];
}

export interface SyncConflict {
  localEvent: CalendarEvent;
  externalEvent: CalendarEvent;
  conflictType: 'title' | 'time' | 'description' | 'location';
}

export interface SyncOperation {
  type: 'export' | 'update' | 'delete' | 'import';
  event: CalendarEvent;
  calendarId?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface BatchSyncResult {
  success: boolean;
  results: SyncOperationResult[];
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
}

export interface SyncOperationResult {
  operation: SyncOperation;
  success: boolean;
  result?: any;
  error?: string;
}
