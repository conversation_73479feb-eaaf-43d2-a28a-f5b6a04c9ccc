import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupData, TodoItem, Reminder, Note, CalendarEvent, Folder, AppSettings } from '@/types';

// Storage keys
const STORAGE_KEYS = {
  BACKUP: '@productivity_app_backup',
  LAST_SYNC: '@productivity_app_last_sync',
  USER_PREFERENCES: '@productivity_app_user_preferences',
  OFFLINE_QUEUE: '@productivity_app_offline_queue',
} as const;

// Storage service for managing app data
export class StorageService {
  // Backup and restore functionality
  static async createBackup(
    todos: TodoItem[],
    reminders: <PERSON>minder[],
    notes: Note[],
    events: CalendarEvent[],
    folders: Folder[],
    settings: AppSettings
  ): Promise<void> {
    try {
      const backupData: BackupData = {
        todos,
        reminders,
        notes,
        events,
        folders,
        settings,
        exportedAt: new Date(),
        version: '1.0.0',
      };

      await AsyncStorage.setItem(STORAGE_KEYS.BACKUP, JSON.stringify(backupData));
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw new Error('Failed to create backup');
    }
  }

  static async restoreFromBackup(): Promise<BackupData | null> {
    try {
      const backupString = await AsyncStorage.getItem(STORAGE_KEYS.BACKUP);
      if (!backupString) return null;

      const backupData: BackupData = JSON.parse(backupString);
      
      // Validate backup data structure
      if (!this.isValidBackupData(backupData)) {
        throw new Error('Invalid backup data format');
      }

      return backupData;
    } catch (error) {
      console.error('Failed to restore backup:', error);
      throw new Error('Failed to restore backup');
    }
  }

  static async exportBackupAsString(): Promise<string> {
    try {
      const backupString = await AsyncStorage.getItem(STORAGE_KEYS.BACKUP);
      if (!backupString) {
        throw new Error('No backup data found');
      }
      return backupString;
    } catch (error) {
      console.error('Failed to export backup:', error);
      throw new Error('Failed to export backup');
    }
  }

  static async importBackupFromString(backupString: string): Promise<BackupData> {
    try {
      const backupData: BackupData = JSON.parse(backupString);
      
      if (!this.isValidBackupData(backupData)) {
        throw new Error('Invalid backup data format');
      }

      await AsyncStorage.setItem(STORAGE_KEYS.BACKUP, backupString);
      return backupData;
    } catch (error) {
      console.error('Failed to import backup:', error);
      throw new Error('Failed to import backup');
    }
  }

  // Sync management
  static async getLastSyncTime(): Promise<Date | null> {
    try {
      const lastSyncString = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
      return lastSyncString ? new Date(lastSyncString) : null;
    } catch (error) {
      console.error('Failed to get last sync time:', error);
      return null;
    }
  }

  static async setLastSyncTime(date: Date): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, date.toISOString());
    } catch (error) {
      console.error('Failed to set last sync time:', error);
    }
  }

  // Offline queue management
  static async addToOfflineQueue(action: OfflineAction): Promise<void> {
    try {
      const queueString = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_QUEUE);
      const queue: OfflineAction[] = queueString ? JSON.parse(queueString) : [];
      
      queue.push({
        ...action,
        timestamp: new Date(),
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      });

      await AsyncStorage.setItem(STORAGE_KEYS.OFFLINE_QUEUE, JSON.stringify(queue));
    } catch (error) {
      console.error('Failed to add to offline queue:', error);
    }
  }

  static async getOfflineQueue(): Promise<OfflineAction[]> {
    try {
      const queueString = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_QUEUE);
      return queueString ? JSON.parse(queueString) : [];
    } catch (error) {
      console.error('Failed to get offline queue:', error);
      return [];
    }
  }

  static async clearOfflineQueue(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.OFFLINE_QUEUE);
    } catch (error) {
      console.error('Failed to clear offline queue:', error);
    }
  }

  static async removeFromOfflineQueue(actionId: string): Promise<void> {
    try {
      const queueString = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_QUEUE);
      if (!queueString) return;

      const queue: OfflineAction[] = JSON.parse(queueString);
      const filteredQueue = queue.filter(action => action.id !== actionId);

      await AsyncStorage.setItem(STORAGE_KEYS.OFFLINE_QUEUE, JSON.stringify(filteredQueue));
    } catch (error) {
      console.error('Failed to remove from offline queue:', error);
    }
  }

  // User preferences
  static async getUserPreferences(): Promise<UserPreferences | null> {
    try {
      const preferencesString = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      return preferencesString ? JSON.parse(preferencesString) : null;
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      return null;
    }
  }

  static async setUserPreferences(preferences: UserPreferences): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to set user preferences:', error);
    }
  }

  // Storage cleanup
  static async clearAllData(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const appKeys = keys.filter(key => key.startsWith('@productivity_app'));
      await AsyncStorage.multiRemove(appKeys);
    } catch (error) {
      console.error('Failed to clear all data:', error);
      throw new Error('Failed to clear all data');
    }
  }

  static async getStorageSize(): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const appKeys = keys.filter(key => key.startsWith('@productivity_app'));
      
      let totalSize = 0;
      for (const key of appKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += new Blob([value]).size;
        }
      }
      
      return totalSize;
    } catch (error) {
      console.error('Failed to get storage size:', error);
      return 0;
    }
  }

  // Data validation
  private static isValidBackupData(data: any): data is BackupData {
    return (
      data &&
      typeof data === 'object' &&
      Array.isArray(data.todos) &&
      Array.isArray(data.reminders) &&
      Array.isArray(data.notes) &&
      Array.isArray(data.events) &&
      Array.isArray(data.folders) &&
      data.settings &&
      typeof data.settings === 'object' &&
      data.exportedAt &&
      data.version
    );
  }

  // Migration utilities
  static async migrateData(fromVersion: string, toVersion: string): Promise<void> {
    try {
      console.log(`Migrating data from version ${fromVersion} to ${toVersion}`);
      
      // Add migration logic here as the app evolves
      // For now, we'll just update the version in the backup
      const backupString = await AsyncStorage.getItem(STORAGE_KEYS.BACKUP);
      if (backupString) {
        const backupData = JSON.parse(backupString);
        backupData.version = toVersion;
        await AsyncStorage.setItem(STORAGE_KEYS.BACKUP, JSON.stringify(backupData));
      }
    } catch (error) {
      console.error('Failed to migrate data:', error);
      throw new Error('Failed to migrate data');
    }
  }

  // Health check
  static async performHealthCheck(): Promise<HealthCheckResult> {
    try {
      const result: HealthCheckResult = {
        isHealthy: true,
        issues: [],
        storageSize: 0,
        lastBackup: null,
        lastSync: null,
      };

      // Check storage size
      result.storageSize = await this.getStorageSize();
      
      // Check if storage is too large (>50MB)
      if (result.storageSize > 50 * 1024 * 1024) {
        result.issues.push('Storage size is too large');
        result.isHealthy = false;
      }

      // Check last backup
      const backupString = await AsyncStorage.getItem(STORAGE_KEYS.BACKUP);
      if (backupString) {
        const backupData = JSON.parse(backupString);
        result.lastBackup = new Date(backupData.exportedAt);
      }

      // Check last sync
      result.lastSync = await this.getLastSyncTime();

      // Check for corrupted data
      try {
        const keys = await AsyncStorage.getAllKeys();
        const appKeys = keys.filter(key => key.startsWith('@productivity_app'));
        
        for (const key of appKeys) {
          const value = await AsyncStorage.getItem(key);
          if (value) {
            JSON.parse(value); // This will throw if data is corrupted
          }
        }
      } catch (parseError) {
        result.issues.push('Corrupted data detected');
        result.isHealthy = false;
      }

      return result;
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        isHealthy: false,
        issues: ['Health check failed'],
        storageSize: 0,
        lastBackup: null,
        lastSync: null,
      };
    }
  }
}

// Types for offline functionality
export interface OfflineAction {
  id?: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  entity: 'todo' | 'reminder' | 'note' | 'event' | 'folder';
  data: any;
  timestamp?: Date;
}

export interface UserPreferences {
  onboardingCompleted: boolean;
  lastAppVersion: string;
  analyticsEnabled: boolean;
  crashReportingEnabled: boolean;
  autoBackupEnabled: boolean;
  autoBackupFrequency: 'daily' | 'weekly' | 'monthly';
  lastAutoBackup?: Date;
}

export interface HealthCheckResult {
  isHealthy: boolean;
  issues: string[];
  storageSize: number;
  lastBackup: Date | null;
  lastSync: Date | null;
}

// Utility functions for data management
export const DataUtils = {
  // Generate unique IDs
  generateId: (): string => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  },

  // Validate data integrity
  validateTodo: (todo: any): todo is TodoItem => {
    return (
      todo &&
      typeof todo.id === 'string' &&
      typeof todo.title === 'string' &&
      typeof todo.status === 'string' &&
      typeof todo.priority === 'string' &&
      todo.createdAt &&
      todo.updatedAt
    );
  },

  validateReminder: (reminder: any): reminder is Reminder => {
    return (
      reminder &&
      typeof reminder.id === 'string' &&
      typeof reminder.title === 'string' &&
      typeof reminder.type === 'string' &&
      typeof reminder.notificationStatus === 'string' &&
      reminder.createdAt &&
      reminder.updatedAt
    );
  },

  validateNote: (note: any): note is Note => {
    return (
      note &&
      typeof note.id === 'string' &&
      typeof note.title === 'string' &&
      typeof note.content === 'string' &&
      note.createdAt &&
      note.updatedAt
    );
  },

  validateEvent: (event: any): event is CalendarEvent => {
    return (
      event &&
      typeof event.id === 'string' &&
      typeof event.title === 'string' &&
      event.startDate &&
      event.endDate &&
      event.createdAt &&
      event.updatedAt
    );
  },

  // Data sanitization
  sanitizeString: (str: string): string => {
    return str.trim().replace(/[<>]/g, '');
  },

  sanitizeHtml: (html: string): string => {
    // Basic HTML sanitization - in a real app, use a proper library
    return html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  },
};
